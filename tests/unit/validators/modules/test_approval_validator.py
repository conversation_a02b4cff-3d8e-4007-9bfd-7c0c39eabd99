"""
Test ApprovalValidator functionality.

ApprovalValidator validates DICOM PS3.3 C.8.8.16 Approval Module compliance.
Tests cover Type 1 requirements, Type 2C conditional logic, enumerated values,
date/time format validation, and comprehensive error message quality.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.approval_validator import ApprovalValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.enums.approval_enums import ApprovalStatus


class TestApprovalValidator:
    """Test ApprovalValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        
        result = ApprovalValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_valid_approved_module_passes_validation(self):
        """Test that valid APPROVED module passes validation without errors."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_rejected_module_passes_validation(self):
        """Test that valid REJECTED module passes validation without errors."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.REJECTED.value
        dataset.ReviewDate = "20240320"
        dataset.ReviewTime = "091500.123456"
        dataset.ReviewerName = "Thompson^Lisa^MD^Dr.^"
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_unapproved_module_passes_validation(self):
        """Test that valid UNAPPROVED module passes validation without errors."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_missing_approval_status_generates_error(self):
        """Test that missing required ApprovalStatus (Type 1) generates validation error."""
        dataset = Dataset()
        # ApprovalStatus is missing
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Missing required element: ApprovalStatus (300E,0002)" in result.errors[0]
    
    def test_approved_missing_review_date_generates_error(self):
        """Test that APPROVED status missing ReviewDate generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        # ReviewDate is missing
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required review information for APPROVED status" in error for error in result.errors)
        assert any("ReviewDate" in error for error in result.errors)
    
    def test_approved_missing_review_time_generates_error(self):
        """Test that APPROVED status missing ReviewTime generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewerName = "Smith^John^MD^^"
        # ReviewTime is missing
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required review information for APPROVED status" in error for error in result.errors)
        assert any("ReviewTime" in error for error in result.errors)
    
    def test_approved_missing_reviewer_name_generates_error(self):
        """Test that APPROVED status missing ReviewerName generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = "143022"
        # ReviewerName is missing
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required review information for APPROVED status" in error for error in result.errors)
        assert any("ReviewerName" in error for error in result.errors)
    
    def test_rejected_missing_all_review_fields_generates_error(self):
        """Test that REJECTED status missing all review fields generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.REJECTED.value
        # All review fields missing
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required review information for REJECTED status" in error for error in result.errors)
        assert any("ReviewDate" in error for error in result.errors)
        assert any("ReviewTime" in error for error in result.errors)
        assert any("ReviewerName" in error for error in result.errors)
    
    def test_unapproved_with_review_info_generates_warning(self):
        """Test that UNAPPROVED status with review info generates inconsistency warning."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.is_valid  # Should still be valid (warning, not error)
        assert result.has_warnings
        assert any("Review information present for UNAPPROVED status" in warning for warning in result.warnings)
        assert any("This may be inconsistent" in warning for warning in result.warnings)
    
    def test_invalid_approval_status_generates_warning(self):
        """Test that invalid ApprovalStatus value generates enumeration warning."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.has_warnings
        assert any("ApprovalStatus (300E,0002) value 'INVALID_STATUS'" in warning for warning in result.warnings)
        assert any("should be one of: APPROVED, UNAPPROVED, REJECTED" in warning for warning in result.warnings)
    
    @pytest.mark.parametrize("status", [
        ApprovalStatus.APPROVED.value,
        ApprovalStatus.UNAPPROVED.value,
        ApprovalStatus.REJECTED.value
    ])
    def test_valid_approval_status_values_pass_enumeration_check(self, status):
        """Test that all valid ApprovalStatus enum values pass enumeration validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = status
        
        config = ValidationConfig(check_enumerated_values=True)
        result = ApprovalValidator.validate(dataset, config)
        
        # Should not have enumeration warnings for valid status values
        enum_warnings = [w for w in result.warnings if "ApprovalStatus (300E,0002)" in w]
        assert len(enum_warnings) == 0
    
    @pytest.mark.parametrize("invalid_date", [
        "2024315",      # Missing zero
        "20241301",     # Invalid month
        "18991231",     # Year too old
        "20-03-15",     # Wrong format
        "invalid"       # Non-numeric
    ])
    def test_invalid_review_date_format_generates_error(self, invalid_date):
        """Test that invalid ReviewDate formats generate validation errors.
        
        Note: Current validator does basic range validation (YYYY 1900-9999, MM 01-12, DD 01-31)
        but does not perform full calendar validation (e.g., detecting Feb 30).
        """
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = invalid_date
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any(f"ReviewDate '{invalid_date}' must be in YYYYMMDD format" in error for error in result.errors)
    
    @pytest.mark.parametrize("valid_date", [
        "20240315",     # Standard format
        "20241231",     # End of year
        "20240101",     # Start of year
        "19951212",     # Earlier valid date
        "99991231",     # Valid year range upper bound
    ])
    def test_valid_review_date_formats_pass_validation(self, valid_date):
        """Test that valid ReviewDate formats pass validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = valid_date
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        
        result = ApprovalValidator.validate(dataset)
        
        # Should not have date format errors
        date_errors = [e for e in result.errors if "ReviewDate" in e and "format" in e]
        assert len(date_errors) == 0
    
    @pytest.mark.parametrize("invalid_time", [
        "1430222",      # Too many digits
        "14302",        # Too few digits  
        "143022.1234567", # Too many fractional digits
        "253022",       # Invalid hour
        "146022",       # Invalid minute
        "143060",       # Invalid second
        "14:30:22",     # Wrong format
        "invalid"       # Non-numeric
    ])
    def test_invalid_review_time_format_generates_error(self, invalid_time):
        """Test that invalid ReviewTime formats generate validation errors."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = invalid_time
        dataset.ReviewerName = "Smith^John^MD^^"
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any(f"ReviewTime '{invalid_time}' must be in HHMMSS or HHMMSS.FFFFFF format" in error for error in result.errors)
    
    @pytest.mark.parametrize("valid_time", [
        "143022",        # Standard format
        "000000",        # Midnight
        "235959",        # End of day
        "143022.123456", # With microseconds
        "143022.1",      # Single fractional digit
        "143022.12345",  # Five fractional digits
    ])
    def test_valid_review_time_formats_pass_validation(self, valid_time):
        """Test that valid ReviewTime formats pass validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = valid_time
        dataset.ReviewerName = "Smith^John^MD^^"
        
        result = ApprovalValidator.validate(dataset)
        
        # Should not have time format errors
        time_errors = [e for e in result.errors if "ReviewTime" in e and "format" in e]
        assert len(time_errors) == 0
    
    def test_validation_config_conditional_requirements_disabled(self):
        """Test that conditional requirement validation can be disabled via config."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        # Missing all review fields
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = ApprovalValidator.validate(dataset, config)
        
        # Should not have conditional requirement errors when disabled
        conditional_errors = [e for e in result.errors if "Missing required review information" in e]
        assert len(conditional_errors) == 0
    
    def test_validation_config_enumerated_values_disabled(self):
        """Test that enumerated value validation can be disabled via config."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        
        config = ValidationConfig(check_enumerated_values=False)
        result = ApprovalValidator.validate(dataset, config)
        
        # Should not have enumeration warnings when disabled
        enum_warnings = [w for w in result.warnings if "ApprovalStatus (300E,0002)" in w]
        assert len(enum_warnings) == 0
    
    def test_validation_with_default_config(self):
        """Test validation behavior with default ValidationConfig."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        
        # Test with None config (should use defaults)
        result = ApprovalValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
    
    def test_error_message_includes_dicom_tag_references(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        # Missing ApprovalStatus
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.has_errors
        assert any("(300E,0002)" in error for error in result.errors)
    
    def test_error_messages_are_human_readable_and_actionable(self):
        """Test that error messages are clear and provide actionable guidance."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        # Missing review information
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.has_errors
        errors = result.errors
        
        # Check that error message explains the context and requirements
        error_text = ' '.join(errors)
        assert "APPROVED status" in error_text
        assert "required review information" in error_text
        assert "ReviewDate" in error_text or "ReviewTime" in error_text or "ReviewerName" in error_text
    
    def test_multiple_validation_issues_are_all_reported(self):
        """Test that multiple validation issues are all captured and reported."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        dataset.ReviewDate = "invalid_date"
        dataset.ReviewTime = "invalid_time"
        
        config = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True
        )
        result = ApprovalValidator.validate(dataset, config)
        
        # Should have multiple issues reported
        assert result.has_warnings or result.has_errors
        assert result.total_issues >= 2  # At least enum warning + format errors
    
    def test_validation_result_consistency_across_scenarios(self):
        """Test that ValidationResult format is consistent across all validation scenarios."""
        test_datasets = [
            Dataset(),  # Empty dataset
            self._create_valid_approved_dataset(),
            self._create_invalid_dataset()
        ]
        
        for dataset in test_datasets:
            result = ApprovalValidator.validate(dataset)
            
            # Verify consistent ValidationResult structure
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert hasattr(result, 'is_valid')
            assert hasattr(result, 'has_errors')
            assert hasattr(result, 'has_warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
    
    def test_validation_performance_within_reasonable_bounds(self):
        """Test that validation completes within reasonable time bounds."""
        import time
        
        dataset = self._create_valid_approved_dataset()
        
        start_time = time.time()
        result = ApprovalValidator.validate(dataset)
        end_time = time.time()
        
        # Validation should complete in well under 1 second
        assert (end_time - start_time) < 1.0
        assert isinstance(result, ValidationResult)
    
    def test_validation_handles_malformed_data_gracefully(self):
        """Test that validator handles malformed data gracefully without crashes."""
        # Test with various malformed data scenarios
        malformed_datasets = [
            None,
            "not_a_dataset",
            Dataset()  # Empty dataset
        ]
        
        for malformed_data in malformed_datasets:
            try:
                if malformed_data is None or not isinstance(malformed_data, Dataset):
                    # Skip invalid inputs that would cause TypeError
                    continue
                result = ApprovalValidator.validate(malformed_data)
                assert isinstance(result, ValidationResult)
            except Exception as e:
                # Should not crash with unhandled exceptions
                pytest.fail(f"Validator crashed with malformed data: {e}")
    
    def _create_valid_approved_dataset(self) -> Dataset:
        """Create a valid APPROVED approval module dataset for testing."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240315"
        dataset.ReviewTime = "143022"
        dataset.ReviewerName = "Smith^John^MD^^"
        return dataset
    
    def _create_invalid_dataset(self) -> Dataset:
        """Create an invalid approval module dataset for testing."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        dataset.ReviewDate = "invalid_date"
        dataset.ReviewTime = "invalid_time"
        return dataset