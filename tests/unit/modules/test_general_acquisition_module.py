"""
Test GeneralAcquisitionModule functionality.

GeneralAcquisitionModule implements DICOM PS3.3 C.7.10.1 General Acquisition Module.
All elements are Type 3 (optional).
"""

from datetime import datetime, date
from pyrt_dicom.modules import GeneralAcquisitionModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralAcquisitionModule:
    """Test GeneralAcquisitionModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Should be able to create empty instance since all elements are Type 3
        assert acquisition is not None
        assert isinstance(acquisition, GeneralAcquisitionModule)
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="1.2.3.4.5.6.7.8.9.10.11.12",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000"
        )
        
        assert hasattr(acquisition, 'AcquisitionUID')
        assert hasattr(acquisition, 'AcquisitionNumber')
        assert hasattr(acquisition, 'AcquisitionDate')
        assert hasattr(acquisition, 'AcquisitionTime')
        
        assert acquisition.AcquisitionUID == "1.2.3.4.5.6.7.8.9.10.11.12"
        assert acquisition.AcquisitionNumber == 1
        assert acquisition.AcquisitionDate == "20240101"
        assert acquisition.AcquisitionTime == "120000"
    
    def test_with_optional_elements_all(self):
        """Test adding all optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="1.2.3.4.5.6.7.8.9.10.11.12",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000",
            acquisition_duration=30.5,
            images_in_acquisition=100,
            irradiation_event_uid="1.2.3.4.5.6.7.8.9.10.11.13"
        )
        
        assert acquisition.AcquisitionUID == "1.2.3.4.5.6.7.8.9.10.11.12"
        assert acquisition.AcquisitionNumber == 1
        assert acquisition.AcquisitionDate == "20240101"
        assert acquisition.AcquisitionTime == "120000"
        assert acquisition.AcquisitionDateTime == "20240101120000.000000"
        assert acquisition.AcquisitionDuration == 30.5
        assert acquisition.ImagesInAcquisition == 100
        assert acquisition.IrradiationEventUID == "1.2.3.4.5.6.7.8.9.10.11.13"
    
    def test_datetime_formatting(self):
        """Test datetime formatting functionality."""
        now = datetime(2024, 1, 1, 12, 0, 0, 123456)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_datetime=now
        )
        
        assert hasattr(acquisition, 'AcquisitionDateTime')
        assert "20240101120000.123456" in acquisition.AcquisitionDateTime
    
    def test_date_formatting(self):
        """Test date formatting functionality."""
        test_date = date(2024, 1, 1)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date=test_date
        )
        
        assert hasattr(acquisition, 'AcquisitionDate')
        assert acquisition.AcquisitionDate == "20240101"
    
    def test_time_formatting(self):
        """Test time formatting functionality."""
        test_time = datetime(2024, 1, 1, 14, 30, 45)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_time=test_time
        )
        
        assert hasattr(acquisition, 'AcquisitionTime')
        assert acquisition.AcquisitionTime == "143045"
    
    def test_property_methods_empty(self):
        """Test property methods with empty module."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is False
        assert acquisition.has_timing_information is False
        assert acquisition.has_irradiation_event is False
        assert acquisition.has_image_count is False
        
        # Test value properties
        assert acquisition.acquisition_uid_value is None
        assert acquisition.acquisition_number_value is None
        assert acquisition.acquisition_date_value is None
        assert acquisition.acquisition_time_value is None
        assert acquisition.acquisition_datetime_value is None
        assert acquisition.acquisition_duration_value is None
        assert acquisition.images_in_acquisition_value is None
        assert acquisition.irradiation_event_uid_value is None
    
    def test_property_methods_populated(self):
        """Test property methods with populated module."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="1.2.3.4.5",
            acquisition_number=5,
            acquisition_date="20240101",
            acquisition_duration=45.0,
            images_in_acquisition=200,
            irradiation_event_uid="1.2.3.4.6"
        )
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is True
        assert acquisition.has_timing_information is True
        assert acquisition.has_irradiation_event is True
        assert acquisition.has_image_count is True
        
        # Test value properties
        assert acquisition.acquisition_uid_value == "1.2.3.4.5"
        assert acquisition.acquisition_number_value == 5
        assert acquisition.acquisition_date_value == "20240101"
        assert acquisition.acquisition_duration_value == 45.0
        assert acquisition.images_in_acquisition_value == 200
        assert acquisition.irradiation_event_uid_value == "1.2.3.4.6"
    
    def test_acquisition_summary_empty(self):
        """Test acquisition summary with empty module."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, dict)
        assert len(summary) == 0
    
    def test_acquisition_summary_populated(self):
        """Test acquisition summary with populated module."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="1.2.3.4.5",
            acquisition_number=10,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_duration=60.0,
            images_in_acquisition=150,
            irradiation_event_uid="1.2.3.4.6"
        )
        
        summary = acquisition.get_acquisition_summary()
        
        assert 'identification' in summary
        assert 'timing' in summary
        assert 'images_count' in summary
        assert 'irradiation_event_uid' in summary
        
        assert summary['identification']['uid'] == "1.2.3.4.5"
        assert summary['identification']['number'] == 10
        assert summary['timing']['date'] == "20240101"
        assert summary['timing']['time'] == "120000"
        assert summary['timing']['duration_seconds'] == 60.0
        assert summary['images_count'] == 150
        assert summary['irradiation_event_uid'] == "1.2.3.4.6"
    
    def test_is_synchronized_with_external_clock(self):
        """Test external clock synchronization method."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # This method should always return False as documented
        assert acquisition.is_synchronized_with_external_clock() is False
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        assert hasattr(acquisition, 'validate')
        assert callable(acquisition.validate)
        
        # Test validation result structure
        validation_result = acquisition.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid=None,
            acquisition_number=None,
            acquisition_date=None
        )
        
        # None values should not create attributes
        assert not hasattr(acquisition, 'AcquisitionUID')
        assert not hasattr(acquisition, 'AcquisitionNumber')
        assert not hasattr(acquisition, 'AcquisitionDate')
