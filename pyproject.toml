[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "pyrt-dicom"
dynamic = ["version"]
description = 'Python library for creating radiotherapy DICOM files'
readme = "README.md"
requires-python = ">=3.11"
license = "MIT"
keywords = ["dicom", "radiotherapy", "rt", "medical", "imaging", "oncology"]
authors = [
  { name = "<PERSON>", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
dependencies = [
  "pydicom>=2.4.0",
]

[project.optional-dependencies]
test = [
  "pytest>=7.0.0",
  "pytest-cov>=4.0.0",
  "ruff >= 0.12.0",
]

[project.urls]
Documentation = "https://github.com/unknown/pyrt-dicom#readme"
Issues = "https://github.com/unknown/pyrt-dicom/issues"
Source = "https://github.com/unknown/pyrt-dicom"

[tool.hatch.version]
path = "src/pyrt_dicom/__about__.py"

[tool.hatch.envs.types]
extra-dependencies = [
  "mypy>=1.0.0",
]
[tool.hatch.envs.types.scripts]
check = "mypy --install-types --non-interactive {args:src/pyrt_dicom tests}"

[tool.coverage.run]
source_pkgs = ["pyrt_dicom", "tests"]
branch = true
parallel = true
omit = [
  "src/pyrt_dicom/__about__.py",
]

[tool.coverage.paths]
pyrt_dicom = ["src/pyrt_dicom", "*/pyrt-dicom/src/pyrt_dicom"]
tests = ["tests", "*/pyrt-dicom/tests"]

[tool.coverage.report]
exclude_lines = [
  "no cov",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
]
