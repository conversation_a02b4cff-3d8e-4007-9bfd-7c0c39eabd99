"""RT Fraction Scheme Module DICOM validation - PS3.3 C.8.8.13"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import BeamDoseMeaning, DoseCalibrationConditionsVerifiedFlag
from ...enums.dose_enums import DoseType


class RTFractionSchemeValidator:
    """Validator for DICOM RT Fraction Scheme Module (PS3.3 C.8.8.13)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate RT Fraction Scheme Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTFractionSchemeValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTFractionSchemeValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTFractionSchemeValidator._validate_sequence_requirements(dataset, result)
        
        # Validate fraction scheme consistency
        RTFractionSchemeValidator._validate_fraction_scheme_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            number_of_beams = fraction_item.get('NumberOfBeams', 0)
            number_of_brachy_setups = fraction_item.get('NumberOfBrachyApplicationSetups', 0)
            
            # Type 1C: Referenced Beam Sequence required if Number of Beams > 0
            if number_of_beams > 0:
                if not fraction_item.get('ReferencedBeamSequence'):
                    result.add_error(
                        f"Fraction Group Sequence item {i}: "
                        "Referenced Beam Sequence (300C,0004) is required when Number of Beams > 0"
                    )
            
            # Type 1C: Referenced Brachy Application Setup Sequence required if Number of Brachy Application Setups > 0
            if number_of_brachy_setups > 0:
                if not fraction_item.get('ReferencedBrachyApplicationSetupSequence'):
                    result.add_error(
                        f"Fraction Group Sequence item {i}: "
                        "Referenced Brachy Application Setup Sequence (300C,000A) is required when "
                        "Number of Brachy Application Setups > 0"
                    )
            
            # Validate Referenced Beam Sequence conditional requirements
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                # Type 1C: Beam Dose Type required if Alternate Beam Dose is present
                if 'AlternateBeamDose' in beam_item and 'BeamDoseType' not in beam_item:
                    result.add_error(
                        f"Referenced Beam Sequence item {j} in Fraction Group {i}: "
                        "Beam Dose Type (300A,0090) is required when Alternate Beam Dose is present"
                    )
                
                # Type 1C: Alternate Beam Dose Type required if Alternate Beam Dose is present
                if 'AlternateBeamDose' in beam_item and 'AlternateBeamDoseType' not in beam_item:
                    result.add_error(
                        f"Referenced Beam Sequence item {j} in Fraction Group {i}: "
                        "Alternate Beam Dose Type (300A,0092) is required when Alternate Beam Dose is present"
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            # Beam Dose Meaning (300A,008B)
            beam_dose_meaning = fraction_item.get('BeamDoseMeaning', '')
            if beam_dose_meaning:
                valid_meanings = [meaning.value for meaning in BeamDoseMeaning]
                BaseValidator.validate_enumerated_value(
                    beam_dose_meaning, valid_meanings,
                    f"Beam Dose Meaning (300A,008B) in Fraction Group {i}", result
                )
            
            # Validate Referenced Beam Sequence enumerated values
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                # Beam Dose Type (300A,0090)
                beam_dose_type = beam_item.get('BeamDoseType', '')
                if beam_dose_type:
                    valid_dose_types = [dtype.value for dtype in DoseType]
                    BaseValidator.validate_enumerated_value(
                        beam_dose_type, valid_dose_types,
                        f"Beam Dose Type (300A,0090) in Beam {j}, Fraction Group {i}", result
                    )
                
                # Alternate Beam Dose Type (300A,0092)
                alt_beam_dose_type = beam_item.get('AlternateBeamDoseType', '')
                if alt_beam_dose_type:
                    valid_dose_types = [dtype.value for dtype in DoseType]
                    BaseValidator.validate_enumerated_value(
                        alt_beam_dose_type, valid_dose_types,
                        f"Alternate Beam Dose Type (300A,0092) in Beam {j}, Fraction Group {i}", result
                    )
                
                # Dose Calibration Conditions Verified Flag (300C,0123)
                calibration_flag = beam_item.get('DoseCalibrationConditionsVerifiedFlag', '')
                if calibration_flag:
                    valid_flags = [flag.value for flag in DoseCalibrationConditionsVerifiedFlag]
                    BaseValidator.validate_enumerated_value(
                        calibration_flag, valid_flags,
                        f"Dose Calibration Conditions Verified Flag (300C,0123) in Beam {j}, Fraction Group {i}", result
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Fraction Group Sequence validation (Type 1)
        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        if not fraction_group_seq:
            result.add_error(
                "Fraction Group Sequence (300A,0070) is required (Type 1)"
            )
        
        for i, fraction_item in enumerate(fraction_group_seq):
            # Fraction Group Number is Type 1
            if not fraction_item.get('FractionGroupNumber'):
                result.add_error(
                    f"Fraction Group Sequence item {i}: "
                    "Fraction Group Number (300A,0071) is required"
                )
            
            # Number of Fractions Planned is Type 2
            if 'NumberOfFractionsPlanned' not in fraction_item:
                result.add_error(
                    f"Fraction Group Sequence item {i}: "
                    "Number of Fractions Planned (300A,0078) is required (Type 2)"
                )
            
            # Number of Beams is Type 1
            if not fraction_item.get('NumberOfBeams'):
                result.add_error(
                    f"Fraction Group Sequence item {i}: "
                    "Number of Beams (300A,0080) is required"
                )
            
            # Number of Brachy Application Setups is Type 1
            if not fraction_item.get('NumberOfBrachyApplicationSetups'):
                result.add_error(
                    f"Fraction Group Sequence item {i}: "
                    "Number of Brachy Application Setups (300A,00A0) is required"
                )
            
            # Validate Referenced Beam Sequence
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                if not beam_item.get('ReferencedBeamNumber'):
                    result.add_error(
                        f"Referenced Beam Sequence item {j} in Fraction Group {i}: "
                        "Referenced Beam Number (300C,0006) is required"
                    )

            # Validate Referenced Dose Sequence
            ref_dose_seq = fraction_item.get('ReferencedDoseSequence', [])
            for j, dose_item in enumerate(ref_dose_seq):
                if not dose_item.get('ReferencedDoseReferenceNumber'):
                    result.add_error(
                        f"Referenced Dose Sequence item {j} in Fraction Group {i}: "
                        "Referenced Dose Reference Number (300A,0012) is required"
                    )
            
            # Validate Referenced Brachy Application Setup Sequence
            ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
            for j, brachy_item in enumerate(ref_brachy_seq):
                if not brachy_item.get('ReferencedBrachyApplicationSetupNumber'):
                    result.add_error(
                        f"Referenced Brachy Application Setup Sequence item {j} in Fraction Group {i}: "
                        "Referenced Brachy Application Setup Number (300C,000C) is required"
                    )
        
        # Validate uniqueness of fraction group numbers
        group_numbers = []
        for i, fraction_item in enumerate(fraction_group_seq):
            group_number = fraction_item.get('FractionGroupNumber')
            if group_number is not None:
                if group_number in group_numbers:
                    result.add_error(
                        f"Fraction Group Sequence item {i}: "
                        f"Fraction Group Number ({group_number}) must be unique within the RT Plan"
                    )
                else:
                    group_numbers.append(group_number)
    
    @staticmethod
    def _validate_fraction_scheme_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate fraction scheme consistency and logical constraints."""
        
        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            number_of_beams = fraction_item.get('NumberOfBeams', 0)
            number_of_brachy_setups = fraction_item.get('NumberOfBrachyApplicationSetups', 0)
            
            # Validate mutual exclusivity of beams and brachy setups
            if number_of_beams > 0 and number_of_brachy_setups > 0:
                result.add_error(
                    f"Fraction Group Sequence item {i}: "
                    "Number of Beams and Number of Brachy Application Setups cannot both be greater than zero"
                )
            
            # Validate beam sequence count matches number of beams
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            if number_of_beams > 0 and len(ref_beam_seq) != number_of_beams:
                result.add_warning(
                    f"Fraction Group Sequence item {i}: "
                    f"Number of Beams ({number_of_beams}) does not match Referenced Beam Sequence length ({len(ref_beam_seq)})"
                )
            
            # Validate brachy sequence count matches number of setups
            ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
            if number_of_brachy_setups > 0 and len(ref_brachy_seq) != number_of_brachy_setups:
                result.add_warning(
                    f"Fraction Group Sequence item {i}: "
                    f"Number of Brachy Application Setups ({number_of_brachy_setups}) does not match "
                    f"Referenced Brachy Application Setup Sequence length ({len(ref_brachy_seq)})"
                )
            
            # Validate fraction pattern consistency
            pattern = fraction_item.get('FractionPattern', '')
            digits_per_day = fraction_item.get('NumberOfFractionPatternDigitsPerDay')
            cycle_length = fraction_item.get('RepeatFractionCycleLength')
            
            if pattern and digits_per_day and cycle_length:
                expected_length = 7 * digits_per_day * cycle_length
                if len(pattern) != expected_length:
                    result.add_error(
                        f"Fraction Group Sequence item {i}: "
                        f"Fraction Pattern length ({len(pattern)}) should be "
                        f"7 × {digits_per_day} × {cycle_length} = {expected_length}"
                    )
                
                # Validate pattern contains only 0s and 1s
                if not all(c in '01' for c in pattern):
                    result.add_error(
                        f"Fraction Group Sequence item {i}: "
                        "Fraction Pattern should contain only '0' and '1' characters"
                    )
            
            # Validate dose values are non-negative
            for j, beam_item in enumerate(ref_beam_seq):
                beam_dose = beam_item.get('BeamDose')
                if beam_dose is not None and beam_dose < 0:
                    result.add_warning(
                        f"Referenced Beam Sequence item {j} in Fraction Group {i}: "
                        f"Beam Dose ({beam_dose}) should be non-negative"
                    )
                
                beam_meterset = beam_item.get('BeamMeterset')
                if beam_meterset is not None and beam_meterset < 0:
                    result.add_warning(
                        f"Referenced Beam Sequence item {j} in Fraction Group {i}: "
                        f"Beam Meterset ({beam_meterset}) should be non-negative"
                    )
