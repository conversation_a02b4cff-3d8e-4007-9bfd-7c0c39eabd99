"""General Acquisition Module Validator - DICOM PS3.3 C.7.10.1"""

import re
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralAcquisitionValidator(BaseValidator):
    """Validator for General Acquisition Module (C.7.10.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate General Acquisition Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate UID formats
        GeneralAcquisitionValidator._validate_uid_formats(dataset, result)
        
        # Validate date/time formats
        GeneralAcquisitionValidator._validate_datetime_formats(dataset, result)
        
        # Validate numeric ranges
        GeneralAcquisitionValidator._validate_numeric_ranges(dataset, result)
        
        # Validate logical consistency
        GeneralAcquisitionValidator._validate_logical_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_uid_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate UID format compliance."""
        
        # UID format pattern: digits and dots, max 64 characters
        uid_pattern = re.compile(r'^[0-9.]+$')
        
        # Acquisition UID (0008,0017)
        acquisition_uid = getattr(dataset, 'AcquisitionUID', '')
        if acquisition_uid:
            if not uid_pattern.match(acquisition_uid):
                result.add_error(
                    f"Acquisition UID (0008,0017) format invalid: '{acquisition_uid}'. "
                    f"Must contain only digits and dots"
                )
            elif len(acquisition_uid) > 64:
                result.add_error(
                    f"Acquisition UID (0008,0017) too long: {len(acquisition_uid)} characters. "
                    f"Maximum length is 64 characters"
                )
            elif acquisition_uid.startswith('.') or acquisition_uid.endswith('.'):
                result.add_error(
                    f"Acquisition UID (0008,0017) cannot start or end with dot: '{acquisition_uid}'"
                )
            elif '..' in acquisition_uid:
                result.add_error(
                    f"Acquisition UID (0008,0017) cannot contain consecutive dots: '{acquisition_uid}'"
                )
        
        # Irradiation Event UID (0008,3010)
        irradiation_uid = getattr(dataset, 'IrradiationEventUID', '')
        if irradiation_uid:
            if not uid_pattern.match(irradiation_uid):
                result.add_error(
                    f"Irradiation Event UID (0008,3010) format invalid: '{irradiation_uid}'. "
                    f"Must contain only digits and dots"
                )
            elif len(irradiation_uid) > 64:
                result.add_error(
                    f"Irradiation Event UID (0008,3010) too long: {len(irradiation_uid)} characters. "
                    f"Maximum length is 64 characters"
                )
            elif irradiation_uid.startswith('.') or irradiation_uid.endswith('.'):
                result.add_error(
                    f"Irradiation Event UID (0008,3010) cannot start or end with dot: '{irradiation_uid}'"
                )
            elif '..' in irradiation_uid:
                result.add_error(
                    f"Irradiation Event UID (0008,3010) cannot contain consecutive dots: '{irradiation_uid}'"
                )
    
    @staticmethod
    def _validate_datetime_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate date and time format compliance."""
        
        # Date format: YYYYMMDD
        date_pattern = re.compile(r'^\d{8}$')
        
        # Time format: HHMMSS or HHMMSS.FFFFFF
        time_pattern = re.compile(r'^\d{6}(\.\d{1,6})?$')
        
        # DateTime format: YYYYMMDDHHMMSS.FFFFFF
        datetime_pattern = re.compile(r'^\d{14}(\.\d{1,6})?$')
        
        # Acquisition Date (0008,0022)
        acquisition_date = getattr(dataset, 'AcquisitionDate', '')
        if acquisition_date:
            if not date_pattern.match(acquisition_date):
                result.add_error(
                    f"Acquisition Date (0008,0022) format invalid: '{acquisition_date}'. "
                    f"Must be YYYYMMDD format"
                )
            else:
                # Validate actual date values
                try:
                    year = int(acquisition_date[:4])
                    month = int(acquisition_date[4:6])
                    day = int(acquisition_date[6:8])
                    
                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid day: {day}"
                        )
                except ValueError:
                    result.add_error(
                        f"Acquisition Date (0008,0022) contains invalid numeric values: '{acquisition_date}'"
                    )
        
        # Acquisition Time (0008,0032)
        acquisition_time = getattr(dataset, 'AcquisitionTime', '')
        if acquisition_time:
            if not time_pattern.match(acquisition_time):
                result.add_error(
                    f"Acquisition Time (0008,0032) format invalid: '{acquisition_time}'. "
                    f"Must be HHMMSS or HHMMSS.FFFFFF format"
                )
            else:
                # Validate actual time values
                try:
                    time_part = acquisition_time.split('.')[0]
                    hour = int(time_part[:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])
                    
                    if hour > 23:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition Time (0008,0032) contains invalid numeric values: '{acquisition_time}'"
                    )
        
        # Acquisition DateTime (0008,002A)
        acquisition_datetime = getattr(dataset, 'AcquisitionDateTime', '')
        if acquisition_datetime:
            if not datetime_pattern.match(acquisition_datetime):
                result.add_error(
                    f"Acquisition DateTime (0008,002A) format invalid: '{acquisition_datetime}'. "
                    f"Must be YYYYMMDDHHMMSS.FFFFFF format"
                )
            else:
                # Validate actual datetime values
                try:
                    datetime_part = acquisition_datetime.split('.')[0]
                    year = int(datetime_part[:4])
                    month = int(datetime_part[4:6])
                    day = int(datetime_part[6:8])
                    hour = int(datetime_part[8:10])
                    minute = int(datetime_part[10:12])
                    second = int(datetime_part[12:14])
                    
                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid day: {day}"
                        )
                    if hour > 23:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition DateTime (0008,002A) contains invalid numeric values: '{acquisition_datetime}'"
                    )
    
    @staticmethod
    def _validate_numeric_ranges(dataset: Dataset, result: ValidationResult) -> None:
        """Validate numeric value ranges."""
        
        # Acquisition Duration (0018,9073) - should be positive
        duration = getattr(dataset, 'AcquisitionDuration', None)
        if duration is not None:
            try:
                duration_float = float(duration)
                if duration_float < 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) should be positive: {duration_float} seconds"
                    )
                elif duration_float == 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) is zero, which may indicate no actual acquisition time"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Acquisition Duration (0018,9073) must be a numeric value: '{duration}'"
                )
        
        # Images in Acquisition (0020,1002) - should be positive integer
        images_count = getattr(dataset, 'ImagesInAcquisition', None)
        if images_count is not None:
            try:
                images_int = int(images_count)
                if images_int < 0:
                    result.add_error(
                        f"Images in Acquisition (0020,1002) cannot be negative: {images_int}"
                    )
                elif images_int == 0:
                    result.add_warning(
                        f"Images in Acquisition (0020,1002) is zero, which may indicate no images were acquired"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Images in Acquisition (0020,1002) must be an integer value: '{images_count}'"
                )
    
    @staticmethod
    def _validate_logical_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between related attributes."""
        
        acquisition_date = getattr(dataset, 'AcquisitionDate', '')
        acquisition_time = getattr(dataset, 'AcquisitionTime', '')
        acquisition_datetime = getattr(dataset, 'AcquisitionDateTime', '')
        
        # If both date/time and datetime are present, they should be consistent
        if acquisition_date and acquisition_time and acquisition_datetime:
            expected_datetime = acquisition_date + acquisition_time
            # Remove fractional seconds for comparison if present
            datetime_base = acquisition_datetime.split('.')[0]
            expected_base = expected_datetime.split('.')[0]
            
            if datetime_base != expected_base:
                result.add_warning(
                    f"Acquisition DateTime (0008,002A) '{acquisition_datetime}' is inconsistent "
                    f"with Acquisition Date '{acquisition_date}' and Time '{acquisition_time}'"
                )
        
        # Warn if only one of date or time is present
        if acquisition_date and not acquisition_time:
            result.add_warning(
                "Acquisition Date (0008,0022) is present but Acquisition Time (0008,0032) is missing. "
                "Consider providing both for complete timing information"
            )
        
        if acquisition_time and not acquisition_date:
            result.add_warning(
                "Acquisition Time (0008,0032) is present but Acquisition Date (0008,0022) is missing. "
                "Consider providing both for complete timing information"
            )
