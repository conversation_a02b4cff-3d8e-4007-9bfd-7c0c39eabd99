"""Device Module DICOM validation - PS3.3 C.7.6.12"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.equipment_enums import DeviceDiameterUnits


class DeviceValidator:
    """Validator for DICOM Device Module (PS3.3 C.7.6.12)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Device Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        DeviceValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            DeviceValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            DeviceValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            DeviceValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        
        # Type 1: Device Sequence (0050,0010)
        if not hasattr(dataset, 'DeviceSequence'):
            result.add_error("Device Sequence (0050,0010) is required (Type 1)")
        elif not dataset.DeviceSequence:
            result.add_error("Device Sequence (0050,0010) cannot be empty (Type 1)")
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements."""
        
        # Type 2C: Device Diameter Units required if Device Diameter is present
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            device_diameter = item.get('DeviceDiameter')
            device_diameter_units = item.get('DeviceDiameterUnits')
            
            if device_diameter is not None and not device_diameter_units:
                result.add_error(
                    f"Device Sequence item {i}: Device Diameter Units (0050,0017) is required "
                    f"when Device Diameter (0050,0016) is present (Type 2C)"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Device Diameter Units (0050,0017)
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            diameter_units = item.get('DeviceDiameterUnits', '')
            if diameter_units:
                valid_units = [unit.value for unit in DeviceDiameterUnits]
                BaseValidator.validate_enumerated_value(
                    diameter_units, valid_units,
                    f"Device Sequence item {i}: Device Diameter Units (0050,0017)", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Device Sequence - each item needs Code Sequence Macro attributes
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            # Code Sequence Macro requires CodeValue, CodingSchemeDesignator, and CodeMeaning
            if not getattr(item, 'CodeValue', None):
                result.add_error(
                    f"Device Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not getattr(item, 'CodingSchemeDesignator', None):
                result.add_error(
                    f"Device Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not getattr(item, 'CodeMeaning', None):
                result.add_error(
                    f"Device Sequence item {i}: Code Meaning (0008,0104) is required"
                )
