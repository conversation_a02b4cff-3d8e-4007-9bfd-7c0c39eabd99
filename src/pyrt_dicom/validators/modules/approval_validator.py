"""Approval Module Validator - DICOM PS3.3 C.8.8.16 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.approval_enums import ApprovalStatus


class ApprovalValidator(BaseValidator):
    """Validator for Approval Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Approval Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        ApprovalValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            ApprovalValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ApprovalValidator._validate_enumerated_values(dataset, result)
        
        # Validate date/time formats
        ApprovalValidator._validate_date_time_formats(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        if not hasattr(dataset, 'ApprovalStatus'):
            result.add_error("Missing required element: ApprovalStatus (300E,0002)")
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements."""
        if not hasattr(dataset, 'ApprovalStatus'):
            return
        
        approval_status = str(dataset.ApprovalStatus)
        requires_review_info = approval_status in [ApprovalStatus.APPROVED.value, ApprovalStatus.REJECTED.value]
        
        if requires_review_info:
            # Review Date, Review Time, and Reviewer Name are required
            required_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
            missing_fields = [field for field in required_fields if not hasattr(dataset, field)]
            
            if missing_fields:
                result.add_error(
                    f"Missing required review information for {approval_status} status: {', '.join(missing_fields)}"
                )
        else:
            # For UNAPPROVED status, review information should not be present
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                review_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
                present_fields = [field for field in review_fields if hasattr(dataset, field)]
                
                if present_fields:
                    result.add_warning(
                        f"Review information present for UNAPPROVED status: {', '.join(present_fields)}. "
                        "This may be inconsistent."
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values."""
        if hasattr(dataset, 'ApprovalStatus'):
            allowed_values = [e.value for e in ApprovalStatus]
            BaseValidator.validate_enumerated_value(
                dataset.ApprovalStatus, allowed_values, 'ApprovalStatus (300E,0002)', result
            )
    
    @staticmethod
    def _validate_date_time_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate date and time format requirements."""
        # Validate Review Date format (YYYYMMDD)
        if hasattr(dataset, 'ReviewDate'):
            review_date = str(dataset.ReviewDate)
            if not ApprovalValidator._is_valid_date_format(review_date):
                result.add_error(
                    f"ReviewDate '{review_date}' must be in YYYYMMDD format"
                )
        
        # Validate Review Time format (HHMMSS or HHMMSS.FFFFFF)
        if hasattr(dataset, 'ReviewTime'):
            review_time = str(dataset.ReviewTime)
            if not ApprovalValidator._is_valid_time_format(review_time):
                result.add_error(
                    f"ReviewTime '{review_time}' must be in HHMMSS or HHMMSS.FFFFFF format"
                )
    
    @staticmethod
    def _is_valid_date_format(date_str: str) -> bool:
        """Check if date string is in valid DICOM DA format (YYYYMMDD)."""
        if len(date_str) != 8:
            return False
        
        try:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            
            # Basic range checks
            if not (1900 <= year <= 9999):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1 <= day <= 31):
                return False
            
            return True
        except ValueError:
            return False
    
    @staticmethod
    def _is_valid_time_format(time_str: str) -> bool:
        """Check if time string is in valid DICOM TM format (HHMMSS or HHMMSS.FFFFFF)."""
        # Remove fractional seconds if present
        if '.' in time_str:
            base_time, fraction = time_str.split('.', 1)
            if len(fraction) > 6:  # Max 6 digits for microseconds
                return False
        else:
            base_time = time_str
        
        if len(base_time) != 6:
            return False
        
        try:
            hour = int(base_time[:2])
            minute = int(base_time[2:4])
            second = int(base_time[4:6])
            
            # Basic range checks
            if not (0 <= hour <= 23):
                return False
            if not (0 <= minute <= 59):
                return False
            if not (0 <= second <= 59):
                return False
            
            return True
        except ValueError:
            return False
