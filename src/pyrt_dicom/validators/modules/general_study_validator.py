"""General Study Module DICOM validation - PS3.3 C.7.2.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralStudyValidator:
    """Validator for DICOM General Study Module (PS3.3 C.7.2.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate General Study Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Check Type 1 requirements
        if not hasattr(dataset, 'StudyInstanceUID') or not dataset.StudyInstanceUID:
            result.add_error("Study Instance UID (0020,000D) is required (Type 1)")
        
        # Validate sequence structures
        if config.validate_sequences:
            GeneralStudyValidator._validate_sequence_requirements(dataset, result)
            GeneralStudyValidator._validate_person_identification_sequences(dataset, result)

        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referring Physician Identification Sequence validation
        referring_phys_seq = getattr(dataset, 'ReferringPhysicianIdentificationSequence', [])
        if len(referring_phys_seq) > 1:
            result.add_error(
                "Referring Physician Identification Sequence (0008,0096): "
                "Only a single Item is permitted in this Sequence"
            )
        
        # Consulting Physician Identification Sequence validation
        consulting_phys_seq = getattr(dataset, 'ConsultingPhysicianIdentificationSequence', [])
        consulting_phys_name = getattr(dataset, 'ConsultingPhysiciansName', '')
        if consulting_phys_seq and consulting_phys_name:
            # Check that number and order correspond if both are present
            consulting_names = consulting_phys_name.split('\\') if isinstance(consulting_phys_name, str) else []
            if len(consulting_phys_seq) != len(consulting_names):
                result.add_warning(
                    "Consulting Physician Identification Sequence (0008,009D): "
                    "Number of items should correspond to Consulting Physician's Name (0008,009C)"
                )
        
        # Physician(s) of Record Identification Sequence validation
        record_phys_seq = getattr(dataset, 'PhysiciansOfRecordIdentificationSequence', [])
        record_phys_name = getattr(dataset, 'PhysiciansOfRecord', '')
        if record_phys_seq and record_phys_name:
            # Check that number and order correspond if both are present
            record_names = record_phys_name.split('\\') if isinstance(record_phys_name, str) else []
            if len(record_phys_seq) != len(record_names):
                result.add_warning(
                    "Physician(s) of Record Identification Sequence (0008,1049): "
                    "Number of items should correspond to Physician(s) of Record (0008,1048)"
                )
        
        # Physician(s) Reading Study Identification Sequence validation
        reading_phys_seq = getattr(dataset, 'PhysiciansReadingStudyIdentificationSequence', [])
        reading_phys_name = getattr(dataset, 'NameOfPhysiciansReadingStudy', '')
        if reading_phys_seq and reading_phys_name:
            # Check that number and order correspond if both are present
            reading_names = reading_phys_name.split('\\') if isinstance(reading_phys_name, str) else []
            if len(reading_phys_seq) != len(reading_names):
                result.add_warning(
                    "Physician(s) Reading Study Identification Sequence (0008,1062): "
                    "Number of items should correspond to Name of Physician(s) Reading Study (0008,1060)"
                )
        
        # Issuer of Accession Number Sequence validation
        accession_issuer_seq = getattr(dataset, 'IssuerOfAccessionNumberSequence', [])
        if len(accession_issuer_seq) > 1:
            result.add_error(
                "Issuer of Accession Number Sequence (0008,0051): "
                "Only a single Item is permitted in this Sequence"
            )
        
        # Requesting Service Code Sequence validation
        requesting_service_seq = getattr(dataset, 'RequestingServiceCodeSequence', [])
        if len(requesting_service_seq) > 1:
            result.add_error(
                "Requesting Service Code Sequence (0032,1034): "
                "Only a single Item is permitted in this Sequence"
            )

    @staticmethod
    def _validate_person_identification_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Person Identification Macro (Table 10-1) requirements in physician identification sequences."""

        # List of physician identification sequences to validate
        sequences_to_validate = [
            ('ReferringPhysicianIdentificationSequence', 'Referring Physician Identification Sequence (0008,0096)'),
            ('ConsultingPhysicianIdentificationSequence', 'Consulting Physician Identification Sequence (0008,009D)'),
            ('PhysiciansOfRecordIdentificationSequence', 'Physician(s) of Record Identification Sequence (0008,1049)'),
            ('PhysiciansReadingStudyIdentificationSequence', 'Physician(s) Reading Study Identification Sequence (0008,1062)')
        ]

        for seq_attr, seq_name in sequences_to_validate:
            sequence = getattr(dataset, seq_attr, [])
            for i, item in enumerate(sequence):
                GeneralStudyValidator._validate_person_identification_item(item, seq_name, i, result)

    @staticmethod
    def _validate_person_identification_item(item: Dataset, seq_name: str, item_index: int, result: ValidationResult) -> None:
        """Validate a single Person Identification Macro item (Table 10-1)."""

        item_prefix = f"{seq_name} Item {item_index + 1}"

        # Validate Person Identification Code Sequence (0040,1101) Type 1
        person_id_code_seq = getattr(item, 'PersonIdentificationCodeSequence', [])
        if not person_id_code_seq:
            result.add_error(f"{item_prefix}: Person Identification Code Sequence (0040,1101) is required (Type 1)")
        else:
            # Validate each code sequence item (Table 8.8-1)
            for j, code_item in enumerate(person_id_code_seq):
                code_prefix = f"{item_prefix} Person ID Code {j + 1}"
                GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

        # Validate Type 1C requirement: Institution Name OR Institution Code Sequence must be present
        institution_name = getattr(item, 'InstitutionName', None)
        institution_code_seq = getattr(item, 'InstitutionCodeSequence', [])

        if not institution_name and not institution_code_seq:
            result.add_error(
                f"{item_prefix}: Either Institution Name (0008,0080) or Institution Code Sequence (0008,0082) "
                "must be present (Type 1C requirement)"
            )

        # Validate Institution Code Sequence if present
        if institution_code_seq:
            if len(institution_code_seq) > 1:
                result.add_error(
                    f"{item_prefix}: Institution Code Sequence (0008,0082) - "
                    "Only a single Item shall be included in this Sequence"
                )
            for j, code_item in enumerate(institution_code_seq):
                code_prefix = f"{item_prefix} Institution Code {j + 1}"
                GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

    @staticmethod
    def _validate_code_sequence_item(code_item: Dataset, item_prefix: str, result: ValidationResult) -> None:
        """Validate a Code Sequence Macro item (Table 8.8-1)."""

        # Check Code Meaning (Type 1)
        if not hasattr(code_item, 'CodeMeaning') or not code_item.CodeMeaning:
            result.add_error(f"{item_prefix}: Code Meaning (0008,0104) is required (Type 1)")

        # Check Type 1C requirements for code values (at least one must be present)
        code_value = getattr(code_item, 'CodeValue', None)
        long_code_value = getattr(code_item, 'LongCodeValue', None)
        urn_code_value = getattr(code_item, 'URNCodeValue', None)

        if not any([code_value, long_code_value, urn_code_value]):
            result.add_error(
                f"{item_prefix}: At least one of Code Value (0008,0100), Long Code Value (0008,0119), "
                "or URN Code Value (0008,0120) must be present (Type 1C requirement)"
            )

        # Check Coding Scheme Designator (Type 1C - required if Code Value or Long Code Value is present)
        coding_scheme_designator = getattr(code_item, 'CodingSchemeDesignator', None)
        if (code_value or long_code_value) and not coding_scheme_designator:
            result.add_error(
                f"{item_prefix}: Coding Scheme Designator (0008,0102) is required when "
                "Code Value or Long Code Value is present (Type 1C requirement)"
            )
