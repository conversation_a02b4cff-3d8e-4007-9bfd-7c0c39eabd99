"""Contrast/Bolus Module Validator - DICOM PS3.3 C.7.6.4"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.contrast_ct_enums import ContrastBolusIngredient


class ContrastBolusValidator(BaseValidator):
    """Validator for Contrast/Bolus Module (C.7.6.4)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Contrast/Bolus Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ContrastBolusValidator._validate_enumerated_values(dataset, result)
        
        # Validate flow consistency
        ContrastBolusValidator._validate_flow_consistency(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ContrastBolusValidator._validate_sequence_structures(dataset, result)
        
        # Validate timing consistency
        ContrastBolusValidator._validate_timing_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Contrast/Bolus Ingredient (0018,1048)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        if ingredient:
            valid_ingredients = [ingredient.value for ingredient in ContrastBolusIngredient]
            BaseValidator.validate_enumerated_value(
                ingredient, valid_ingredients,
                "Contrast/Bolus Ingredient (0018,1048)", result
            )
    
    @staticmethod
    def _validate_flow_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency between flow rate and flow duration."""
        
        flow_rate = getattr(dataset, 'ContrastFlowRate', [])
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # If both are present, they should have the same number of values
        if flow_rate and flow_duration:
            if len(flow_rate) != len(flow_duration):
                result.add_error(
                    f"Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047) "
                    f"must have the same number of values. "
                    f"Flow Rate has {len(flow_rate)} values, Flow Duration has {len(flow_duration)} values"
                )
    
    @staticmethod
    def _validate_sequence_structures(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structures and their code items."""
        
        # Validate Contrast/Bolus Agent Sequence
        agent_seq = getattr(dataset, 'ContrastBolusAgentSequence', [])
        for i, agent_item in enumerate(agent_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                agent_item, f"Contrast/Bolus Agent Sequence item {i+1}", result
            )
        
        # Validate Administration Route Sequence
        route_seq = getattr(dataset, 'ContrastBolusAdministrationRouteSequence', [])
        if len(route_seq) > 1:
            result.add_warning(
                "Contrast/Bolus Administration Route Sequence (0018,0014) "
                "should contain only a single Item according to DICOM standard"
            )
        
        for i, route_item in enumerate(route_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                route_item, f"Administration Route Sequence item {i+1}", result
            )
            
            # Validate nested Additional Drug Sequence
            drug_seq = route_item.get('AdditionalDrugSequence', [])
            for j, drug_item in enumerate(drug_seq):
                ContrastBolusValidator._validate_code_sequence_item(
                    drug_item, f"Additional Drug Sequence item {j+1} in route item {i+1}", result
                )
    
    @staticmethod
    def _validate_code_sequence_item(item: dict, item_description: str, result: ValidationResult) -> None:
        """Validate a code sequence item structure."""
        
        # Code Value is required
        if 'CodeValue' not in item:
            result.add_error(f"Code Value is required in {item_description}")
        
        # Coding Scheme Designator is required
        if 'CodingSchemeDesignator' not in item:
            result.add_error(f"Coding Scheme Designator is required in {item_description}")
        
        # Code Meaning is recommended but not required
        if 'CodeMeaning' not in item:
            result.add_warning(f"Code Meaning is recommended in {item_description}")
    
    @staticmethod
    def _validate_timing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate timing information consistency."""
        
        start_time = getattr(dataset, 'ContrastBolusStartTime', '')
        stop_time = getattr(dataset, 'ContrastBolusStopTime', '')
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # If both start and stop times are present, validate order
        if start_time and stop_time:
            try:
                # Parse DICOM time format (HHMMSS or HHMMSS.FFFFFF)
                start_parts = start_time.split('.')
                stop_parts = stop_time.split('.')
                
                start_hhmmss = start_parts[0]
                stop_hhmmss = stop_parts[0]
                
                if len(start_hhmmss) >= 6 and len(stop_hhmmss) >= 6:
                    start_seconds = (int(start_hhmmss[:2]) * 3600 + 
                                   int(start_hhmmss[2:4]) * 60 + 
                                   int(start_hhmmss[4:6]))
                    stop_seconds = (int(stop_hhmmss[:2]) * 3600 + 
                                  int(stop_hhmmss[2:4]) * 60 + 
                                  int(stop_hhmmss[4:6]))
                    
                    if start_seconds >= stop_seconds:
                        result.add_warning(
                            "Contrast/Bolus Start Time (0018,1042) should be before "
                            "Contrast/Bolus Stop Time (0018,1043)"
                        )
            except (ValueError, IndexError):
                result.add_warning(
                    "Could not validate time order due to invalid time format"
                )
        
        # Validate that flow duration is an alternative to stop time
        if stop_time and flow_duration:
            result.add_warning(
                "Both Contrast/Bolus Stop Time (0018,1043) and Contrast Flow Duration (0018,1047) "
                "are present. Flow duration is an alternate method of specifying stop time"
            )
    
    @staticmethod
    def validate_example_consistency(dataset: Dataset) -> ValidationResult:
        """Validate against the example provided in DICOM standard.
        
        Example from standard:
        - 100 ml injection of 76% Diatrizoate and meglumine/sodium, diluted 1:1
        - Contrast/Bolus Agent: "76% Diatrizoate"
        - Contrast/Bolus Volume: 100 ml
        - Contrast/Bolus Total Dose: 50 ml
        - Contrast/Bolus Ingredient: "IODINE"
        - Contrast/Bolus Ingredient Concentration: 370mg/ml
        """
        result = BaseValidator.create_validation_result()
        
        agent = getattr(dataset, 'ContrastBolusAgent', '')
        volume = getattr(dataset, 'ContrastBolusVolume', None)
        total_dose = getattr(dataset, 'ContrastBolusTotalDose', None)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        concentration = getattr(dataset, 'ContrastBolusIngredientConcentration', None)
        
        # Check for logical consistency
        if volume is not None and total_dose is not None:
            if total_dose > volume:
                result.add_warning(
                    f"Contrast/Bolus Total Dose ({total_dose} ml) should not exceed "
                    f"Contrast/Bolus Volume ({volume} ml). Total dose represents "
                    f"undiluted contrast agent amount"
                )
        
        # Check ingredient and concentration consistency
        if ingredient and not concentration:
            result.add_warning(
                "Contrast/Bolus Ingredient Concentration (0018,1049) is recommended "
                "when Contrast/Bolus Ingredient (0018,1048) is specified"
            )
        
        if concentration and not ingredient:
            result.add_warning(
                "Contrast/Bolus Ingredient (0018,1048) is recommended "
                "when Contrast/Bolus Ingredient Concentration (0018,1049) is specified"
            )
        
        return result
