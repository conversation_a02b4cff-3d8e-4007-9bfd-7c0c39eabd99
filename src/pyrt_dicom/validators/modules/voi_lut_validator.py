"""VOI LUT Module Validator - DICOM PS3.3 C.11.2 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import VoiLutFunction


class VoiLutValidator(BaseValidator):
    """Validator for VOI LUT Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate VOI LUT Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate conditional requirements
        VoiLutValidator._validate_conditional_requirements(dataset, result)
        
        # Validate VOI LUT Sequence if present
        if hasattr(dataset, 'VOILUTSequence'):
            VoiLutValidator._validate_voi_lut_sequence(dataset, result, config)
        
        # Validate Window parameters if present
        if hasattr(dataset, 'WindowCenter') or hasattr(dataset, 'WindowWidth'):
            VoiLutValidator._validate_window_parameters(dataset, result, config)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate conditional requirements for VOI LUT or Window parameters."""
        has_voi_lut = hasattr(dataset, 'VOILUTSequence')
        has_window_center = hasattr(dataset, 'WindowCenter')
        has_window_width = hasattr(dataset, 'WindowWidth')
        
        # If neither VOI LUT nor Window Center is present, that's valid (identity transformation)
        # But if Window Center is present, Window Width must also be present
        if has_window_center and not has_window_width:
            result.add_error("Window Width is required when Window Center is present")
        elif has_window_width and not has_window_center:
            result.add_error("Window Center is required when Window Width is present")
    
    @staticmethod
    def _validate_voi_lut_sequence(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate VOI LUT Sequence requirements."""
        if not hasattr(dataset, 'VOILUTSequence'):
            return
        
        sequence = dataset.VOILUTSequence
        
        # Must contain at least one item
        if len(sequence) == 0:
            result.add_error("VOI LUT Sequence must contain at least one item")
            return
        
        # Validate each sequence item
        for i, item in enumerate(sequence):
            # Validate required elements in sequence item
            required_fields = ['LUTDescriptor', 'LUTData']
            for field in required_fields:
                if not hasattr(item, field):
                    result.add_error(f"Missing required field {field} in VOI LUT Sequence item {i+1}")
            
            # Validate LUT Descriptor format
            if hasattr(item, 'LUTDescriptor'):
                lut_desc = item.LUTDescriptor
                if not isinstance(lut_desc, (list, tuple)) or len(lut_desc) != 3:
                    result.add_error(f"LUT Descriptor in item {i+1} must contain exactly 3 values")
                else:
                    # Validate third value (bits per entry) - for VOI LUT can be 8-16
                    if not (8 <= lut_desc[2] <= 16):
                        result.add_error(f"LUT Descriptor third value in item {i+1} must be between 8 and 16")
    
    @staticmethod
    def _validate_window_parameters(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate Window Center and Width parameters."""
        if not (hasattr(dataset, 'WindowCenter') and hasattr(dataset, 'WindowWidth')):
            return
        
        try:
            # Parse window center and width values (can be multi-valued)
            center_str = str(dataset.WindowCenter)
            width_str = str(dataset.WindowWidth)
            
            center_values = [float(v.strip()) for v in center_str.split('\\') if v.strip()]
            width_values = [float(v.strip()) for v in width_str.split('\\') if v.strip()]
            
            # Must have same number of values
            if len(center_values) != len(width_values):
                result.add_error(
                    f"Window Center and Width must have same number of values: "
                    f"Center has {len(center_values)}, Width has {len(width_values)}"
                )
            
            # Window Width must be >= 1
            for i, width in enumerate(width_values):
                if width < 1:
                    result.add_error(f"Window Width value {i+1} must be >= 1, found {width}")
            
            # Validate Window Center & Width Explanation if present
            if hasattr(dataset, 'WindowCenterWidthExplanation'):
                explanation_str = str(dataset.WindowCenterWidthExplanation)
                explanation_values = [v.strip() for v in explanation_str.split('\\') if v.strip()]
                
                if len(explanation_values) != len(center_values):
                    result.add_warning(
                        f"Window Center & Width Explanation should have same number of values as Window Center/Width pairs: "
                        f"Explanation has {len(explanation_values)}, pairs have {len(center_values)}"
                    )
        
        except (ValueError, TypeError) as e:
            result.add_error(f"Invalid Window Center or Width values: {str(e)}")
        
        # Validate VOI LUT Function enumerated values
        if config.check_enumerated_values and hasattr(dataset, 'VOILUTFunction'):
            allowed_values = [e.value for e in VoiLutFunction]
            BaseValidator.validate_enumerated_value(
                dataset.VOILUTFunction, allowed_values, 'VOILUTFunction', result
            )
        
        # Validate VOI LUT Function requirements
        if hasattr(dataset, 'VOILUTFunction'):
            voi_function = str(dataset.VOILUTFunction)
            if voi_function == VoiLutFunction.SIGMOID.value:
                # For SIGMOID, Window Width must be > 0 (not just >= 1)
                try:
                    width_str = str(dataset.WindowWidth)
                    width_values = [float(v.strip()) for v in width_str.split('\\') if v.strip()]
                    for i, width in enumerate(width_values):
                        if width <= 0:
                            result.add_error(
                                f"Window Width value {i+1} must be > 0 for SIGMOID function, found {width}"
                            )
                except (ValueError, TypeError):
                    pass  # Already handled above
            elif voi_function == VoiLutFunction.LINEAR_EXACT.value:
                # For LINEAR_EXACT, Window Width must be > 0
                try:
                    width_str = str(dataset.WindowWidth)
                    width_values = [float(v.strip()) for v in width_str.split('\\') if v.strip()]
                    for i, width in enumerate(width_values):
                        if width <= 0:
                            result.add_error(
                                f"Window Width value {i+1} must be > 0 for LINEAR_EXACT function, found {width}"
                            )
                except (ValueError, TypeError):
                    pass  # Already handled above
