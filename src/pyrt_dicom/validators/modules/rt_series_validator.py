"""RT Series Module DICOM validation - PS3.3 C.8.8.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class RTSeriesValidator:
    """Validator for DICOM RT Series Module (PS3.3 C.8.8.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate RT Series Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Check Type 1 requirements
        if not hasattr(dataset, 'Modality') or not dataset.Modality:
            result.add_error("Modality (0008,0060) is required (Type 1)")
        
        if not hasattr(dataset, 'SeriesInstanceUID') or not dataset.SeriesInstanceUID:
            result.add_error("Series Instance UID (0020,000E) is required (Type 1)")
        
        # Check Type 2 requirements
        if not hasattr(dataset, 'OperatorsName'):
            result.add_warning("Operators' Name (0008,1070) is recommended (Type 2)")
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTSeriesValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTSeriesValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Modality (0008,0060) - RT-specific values
        modality = getattr(dataset, 'Modality', '')
        if modality:
            rt_modalities = ["RTIMAGE", "RTDOSE", "RTSTRUCT", "RTPLAN", "RTRECORD"]
            BaseValidator.validate_enumerated_value(
                modality, rt_modalities,
                "Modality (0008,0060)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Operator Identification Sequence validation
        operator_seq = getattr(dataset, 'OperatorIdentificationSequence', [])
        operator_name = getattr(dataset, 'OperatorsName', '')
        if operator_seq and operator_name:
            # Check that number and order correspond if both are present
            operator_names = operator_name.split('\\') if isinstance(operator_name, str) else []
            if len(operator_seq) != len(operator_names):
                result.add_warning(
                    "Operator Identification Sequence (0008,1072): "
                    "Number of items should correspond to Operators' Name (0008,1070)"
                )
        
        # Series Description Code Sequence validation
        series_desc_seq = getattr(dataset, 'SeriesDescriptionCodeSequence', [])
        if len(series_desc_seq) > 1:
            result.add_error(
                "Series Description Code Sequence (0008,103F): "
                "Only a single Item is permitted in this Sequence"
            )
