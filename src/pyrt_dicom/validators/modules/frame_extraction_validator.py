"""Frame Extraction Module Validator - DICOM PS3.3 C.12.3 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class FrameExtractionValidator(BaseValidator):
    """Validator for Frame Extraction Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Frame Extraction Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        FrameExtractionValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structure
        if config.validate_sequences and hasattr(dataset, 'FrameExtractionSequence'):
            FrameExtractionValidator._validate_sequence_structure(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        if not hasattr(dataset, 'FrameExtractionSequence'):
            result.add_error("Missing required element: FrameExtractionSequence (0008,1164)")
            return
        
        sequence = dataset.FrameExtractionSequence
        if len(sequence) == 0:
            result.add_error("FrameExtractionSequence must contain at least one item")
    
    @staticmethod
    def _validate_sequence_structure(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Frame Extraction Sequence structure and content."""
        if not hasattr(dataset, 'FrameExtractionSequence'):
            return
        
        sequence = dataset.FrameExtractionSequence
        
        for i, item in enumerate(sequence):
            item_prefix = f"FrameExtractionSequence item {i+1}"
            
            # Validate required Multi-frame Source SOP Instance UID
            if not hasattr(item, 'MultiFrameSourceSOPInstanceUID'):
                result.add_error(f"{item_prefix}: Missing required MultiFrameSourceSOPInstanceUID")
            else:
                # Validate UID format
                uid = str(item.MultiFrameSourceSOPInstanceUID)
                if not FrameExtractionValidator._is_valid_uid(uid):
                    result.add_error(f"{item_prefix}: Invalid MultiFrameSourceSOPInstanceUID format")
            
            # Validate conditional frame list requirements (exactly one must be present)
            frame_list_fields = ['SimpleFrameList', 'CalculatedFrameList', 'TimeRange']
            present_fields = [field for field in frame_list_fields if hasattr(item, field)]
            
            if len(present_fields) == 0:
                result.add_error(
                    f"{item_prefix}: One of SimpleFrameList, CalculatedFrameList, or TimeRange must be present"
                )
            elif len(present_fields) > 1:
                result.add_error(
                    f"{item_prefix}: Only one of SimpleFrameList, CalculatedFrameList, or TimeRange may be present, "
                    f"found: {', '.join(present_fields)}"
                )
            else:
                # Validate the specific frame list type
                field = present_fields[0]
                if field == 'SimpleFrameList':
                    FrameExtractionValidator._validate_simple_frame_list(item, result, item_prefix)
                elif field == 'CalculatedFrameList':
                    FrameExtractionValidator._validate_calculated_frame_list(item, result, item_prefix)
                elif field == 'TimeRange':
                    FrameExtractionValidator._validate_time_range(item, result, item_prefix)
    
    @staticmethod
    def _validate_simple_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Simple Frame List format."""
        if not hasattr(item, 'SimpleFrameList'):
            return
        
        frame_list = item.SimpleFrameList
        
        if not isinstance(frame_list, (list, tuple)):
            result.add_error(f"{item_prefix}: SimpleFrameList must be a list")
            return
        
        if len(frame_list) == 0:
            result.add_error(f"{item_prefix}: SimpleFrameList must contain at least one frame number")
            return
        
        # Validate frame numbers are positive integers
        for j, frame_num in enumerate(frame_list):
            try:
                frame_int = int(frame_num)
                if frame_int <= 0:
                    result.add_error(f"{item_prefix}: SimpleFrameList frame {j+1} must be positive, found {frame_int}")
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: SimpleFrameList frame {j+1} must be an integer, found {frame_num}")
    
    @staticmethod
    def _validate_calculated_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Calculated Frame List format (triplets of start, end, increment)."""
        if not hasattr(item, 'CalculatedFrameList'):
            return
        
        frame_list = item.CalculatedFrameList
        
        if not isinstance(frame_list, (list, tuple)):
            result.add_error(f"{item_prefix}: CalculatedFrameList must be a list")
            return
        
        if len(frame_list) % 3 != 0:
            result.add_error(f"{item_prefix}: CalculatedFrameList must contain triplets (start, end, increment)")
            return
        
        if len(frame_list) == 0:
            result.add_error(f"{item_prefix}: CalculatedFrameList must contain at least one triplet")
            return
        
        # Validate triplets
        for j in range(0, len(frame_list), 3):
            triplet_num = j // 3 + 1
            try:
                start = int(frame_list[j])
                end = int(frame_list[j + 1])
                increment = int(frame_list[j + 2])
                
                if start <= 0:
                    result.add_error(f"{item_prefix}: CalculatedFrameList triplet {triplet_num} start must be positive")
                if end <= 0:
                    result.add_error(f"{item_prefix}: CalculatedFrameList triplet {triplet_num} end must be positive")
                if increment <= 0:
                    result.add_error(f"{item_prefix}: CalculatedFrameList triplet {triplet_num} increment must be positive")
                if start > end:
                    result.add_error(f"{item_prefix}: CalculatedFrameList triplet {triplet_num} start must be <= end")
                
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: CalculatedFrameList triplet {triplet_num} values must be integers")
    
    @staticmethod
    def _validate_time_range(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Time Range format (start and end times)."""
        if not hasattr(item, 'TimeRange'):
            return
        
        time_range = item.TimeRange
        
        if not isinstance(time_range, (list, tuple)):
            result.add_error(f"{item_prefix}: TimeRange must be a list")
            return
        
        if len(time_range) != 2:
            result.add_error(f"{item_prefix}: TimeRange must contain exactly 2 values (start, end)")
            return
        
        # Validate time format (HHMMSS or HHMMSS.FFFFFF)
        for j, time_val in enumerate(time_range):
            time_name = "start" if j == 0 else "end"
            if not FrameExtractionValidator._is_valid_time_format(str(time_val)):
                result.add_error(f"{item_prefix}: TimeRange {time_name} time must be in HHMMSS format")
    
    @staticmethod
    def _is_valid_uid(uid_str: str) -> bool:
        """Check if UID string is in valid format."""
        if not uid_str:
            return False
        
        # Basic UID format check: numbers and dots, no leading/trailing dots
        if uid_str.startswith('.') or uid_str.endswith('.'):
            return False
        
        parts = uid_str.split('.')
        if len(parts) < 2:  # UID must have at least 2 parts
            return False
        
        for part in parts:
            if not part.isdigit() or part.startswith('0') and len(part) > 1:
                return False
        
        return True
    
    @staticmethod
    def _is_valid_time_format(time_str: str) -> bool:
        """Check if time string is in valid DICOM TM format."""
        # Remove fractional seconds if present
        if '.' in time_str:
            base_time, fraction = time_str.split('.', 1)
            if len(fraction) > 6:  # Max 6 digits for microseconds
                return False
        else:
            base_time = time_str
        
        if len(base_time) != 6:
            return False
        
        try:
            hour = int(base_time[:2])
            minute = int(base_time[2:4])
            second = int(base_time[4:6])
            
            return (0 <= hour <= 23) and (0 <= minute <= 59) and (0 <= second <= 59)
        except ValueError:
            return False
