"""Frame of Reference Module DICOM validation - PS3.3 C.7.4.1"""

import re
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class FrameOfReferenceValidator:
    """Validator for DICOM Frame of Reference Module (PS3.3 C.7.4.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Frame of Reference Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        FrameOfReferenceValidator._validate_type1_requirements(dataset, result)
        
        # Validate UID format
        FrameOfReferenceValidator._validate_uid_format(dataset, result)
        
        # Validate position reference indicator values
        FrameOfReferenceValidator._validate_position_reference_indicator(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes."""
        
        # Frame of Reference UID (0020,0052) Type 1
        if not hasattr(dataset, 'FrameOfReferenceUID'):
            result.add_error("Frame of Reference UID (0020,0052) is required (Type 1)")
        else:
            if not isinstance(dataset.FrameOfReferenceUID, str) or len(dataset.FrameOfReferenceUID.strip()) == 0:
                result.add_error("Frame of Reference UID (0020,0052) must be a non-empty string")
        
        # Position Reference Indicator (0020,1040) Type 2 - required but may be empty
        if not hasattr(dataset, 'PositionReferenceIndicator'):
            result.add_error("Position Reference Indicator (0020,1040) is required (Type 2)")
    
    @staticmethod
    def _validate_uid_format(dataset: Dataset, result: ValidationResult) -> None:
        """Validate UID format according to DICOM standard."""
        
        if not hasattr(dataset, 'FrameOfReferenceUID'):
            return
        
        uid = dataset.FrameOfReferenceUID
        
        # UID format validation according to DICOM PS3.5
        # UIDs are composed of numeric components separated by periods
        # Each component is a series of digits
        # Maximum length is 64 characters
        # No leading zeros except for "0" itself
        
        if len(uid) > 64:
            result.add_error(
                f"Frame of Reference UID (0020,0052) exceeds maximum length of 64 characters (length: {len(uid)})"
            )
        
        # Check basic format: digits and periods only
        if not re.match(r'^[0-9.]+$', uid):
            result.add_error(
                "Frame of Reference UID (0020,0052) contains invalid characters. Only digits and periods are allowed."
            )
        else:
            # Check for valid structure
            if uid.startswith('.') or uid.endswith('.'):
                result.add_error(
                    "Frame of Reference UID (0020,0052) cannot start or end with a period"
                )
            
            if '..' in uid:
                result.add_error(
                    "Frame of Reference UID (0020,0052) cannot contain consecutive periods"
                )
            
            # Check each component
            components = uid.split('.')
            for i, component in enumerate(components):
                if len(component) == 0:
                    result.add_error(
                        f"Frame of Reference UID (0020,0052) has empty component at position {i+1}"
                    )
                elif len(component) > 1 and component.startswith('0'):
                    result.add_error(
                        f"Frame of Reference UID (0020,0052) component '{component}' has leading zero"
                    )
    
    @staticmethod
    def _validate_position_reference_indicator(dataset: Dataset, result: ValidationResult) -> None:
        """Validate position reference indicator values."""
        
        if not hasattr(dataset, 'PositionReferenceIndicator'):
            return
        
        indicator = dataset.PositionReferenceIndicator
        
        # Known valid anatomical reference points for patient-based coordinate systems
        known_anatomical_indicators = {
            "ILIAC_CREST", "ORBITAL_MEDIAL", "STERNAL_NOTCH", "SYMPHYSIS_PUBIS",
            "XIPHOID", "LOWER_COSTAL_MARGIN", "EXTERNAL_AUDITORY_MEATUS"
        }
        
        # Known valid special indicators
        known_special_indicators = {
            "SLIDE_CORNER",      # For slide-related frame of reference
            "CORNEAL_VERTEX_R",  # For corneal coordinate system (right eye)
            "CORNEAL_VERTEX_L"   # For corneal coordinate system (left eye)
        }
        
        all_known_indicators = known_anatomical_indicators | known_special_indicators
        
        if len(indicator.strip()) > 0:  # Only validate if not empty (empty is allowed)
            if indicator not in all_known_indicators:
                result.add_warning(
                    f"Position Reference Indicator (0020,1040) value '{indicator}' is not a standard DICOM value. "
                    f"Known values include: {', '.join(sorted(all_known_indicators))}"
                )
            
            # Specific validation for corneal indicators
            if indicator in ["CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L"]:
                result.add_warning(
                    f"Position Reference Indicator '{indicator}' indicates a corneal coordinate system. "
                    "Ensure this is appropriate for the imaging context."
                )
            
            # Specific validation for slide indicator
            if indicator == "SLIDE_CORNER":
                result.add_warning(
                    "Position Reference Indicator 'SLIDE_CORNER' indicates a slide-based coordinate system. "
                    "Ensure this is appropriate for the imaging context."
                )
