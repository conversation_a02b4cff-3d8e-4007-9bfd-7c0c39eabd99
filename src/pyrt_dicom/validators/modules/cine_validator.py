"""Cine Module DICOM validation - PS3.3 C.7.6.5"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import PreferredPlaybackSequencing, ChannelMode


class CineValidator:
    """Validator for DICOM Cine Module (PS3.3 C.7.6.5)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Cine Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            CineValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            CineValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            CineValidator._validate_sequence_requirements(dataset, result)
        
        # Validate timing consistency
        CineValidator._validate_timing_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Frame Time (0018,1063) Type 1C - Required if Frame Increment Pointer points to it
        # Frame Time Vector (0018,1065) Type 1C - Required if Frame Increment Pointer points to it
        # Note: We can't validate this without access to the Frame Increment Pointer from Multi-frame Module
        # So we'll just check for basic consistency
        
        has_frame_time = hasattr(dataset, 'FrameTime')
        has_frame_time_vector = hasattr(dataset, 'FrameTimeVector')
        
        if has_frame_time and has_frame_time_vector:
            result.add_warning(
                "Both Frame Time (0018,1063) and Frame Time Vector (0018,1065) are present. "
                "Typically only one should be used based on Frame Increment Pointer."
            )
        
        # Validate Frame Time value
        if has_frame_time:
            try:
                frame_time = float(dataset.FrameTime)
                if frame_time < 0:
                    result.add_error("Frame Time (0018,1063) must be non-negative")
            except (ValueError, TypeError):
                result.add_error("Frame Time (0018,1063) must be numeric")
        
        # Validate Frame Time Vector
        if has_frame_time_vector:
            if not isinstance(dataset.FrameTimeVector, (list, tuple)):
                result.add_error("Frame Time Vector (0018,1065) must be a list of values")
            else:
                try:
                    time_vector = [float(x) for x in dataset.FrameTimeVector]
                    if len(time_vector) > 0 and time_vector[0] != 0:
                        result.add_warning(
                            "Frame Time Vector (0018,1065) first value should be 0 "
                            "(first frame always has time increment of 0)"
                        )
                    
                    if any(t < 0 for t in time_vector):
                        result.add_error("Frame Time Vector (0018,1065) values must be non-negative")
                        
                except (ValueError, TypeError):
                    result.add_error("Frame Time Vector (0018,1065) values must be numeric")
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Preferred Playback Sequencing
        if hasattr(dataset, 'PreferredPlaybackSequencing'):
            valid_values = [e.value for e in PreferredPlaybackSequencing]
            BaseValidator.validate_enumerated_value(
                str(dataset.PreferredPlaybackSequencing),
                valid_values,
                "Preferred Playback Sequencing (0018,1244)",
                result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Multiplexed Audio Channels Description Code Sequence validation
        if hasattr(dataset, 'MultiplexedAudioChannelsDescriptionCodeSequence'):
            audio_sequence = dataset.MultiplexedAudioChannelsDescriptionCodeSequence
            
            if not isinstance(audio_sequence, (list, tuple)):
                result.add_error(
                    "Multiplexed Audio Channels Description Code Sequence (003A,0300) must be a sequence"
                )
            else:
                for i, item in enumerate(audio_sequence):
                    item_prefix = f"Audio channel item {i+1}"
                    
                    # Required attributes in each item
                    if not hasattr(item, 'ChannelIdentificationCode'):
                        result.add_error(
                            f"{item_prefix}: Channel Identification Code (003A,0301) is required"
                        )
                    else:
                        try:
                            channel_id = int(item.ChannelIdentificationCode)
                            if channel_id < 1 or channel_id > 9:
                                result.add_warning(
                                    f"{item_prefix}: Channel Identification Code should be 1-9 "
                                    f"(1=main, 2=second, 3-9=complementary)"
                                )
                        except (ValueError, TypeError):
                            result.add_error(
                                f"{item_prefix}: Channel Identification Code (003A,0301) must be numeric"
                            )
                    
                    if not hasattr(item, 'ChannelMode'):
                        result.add_error(
                            f"{item_prefix}: Channel Mode (003A,0302) is required"
                        )
                    else:
                        valid_modes = [e.value for e in ChannelMode]
                        if item.ChannelMode not in valid_modes:
                            result.add_error(
                                f"{item_prefix}: Channel Mode (003A,0302) has invalid value '{item.ChannelMode}'. "
                                f"Valid values: {valid_modes}"
                            )
                    
                    if not hasattr(item, 'ChannelSourceSequence'):
                        result.add_error(
                            f"{item_prefix}: Channel Source Sequence (003A,0208) is required"
                        )
                    else:
                        source_seq = item.ChannelSourceSequence
                        if not isinstance(source_seq, (list, tuple)) or len(source_seq) != 1:
                            result.add_error(
                                f"{item_prefix}: Channel Source Sequence (003A,0208) must contain exactly one item"
                            )
                        elif len(source_seq) == 1:
                            source_item = source_seq[0]
                            if not hasattr(source_item, 'CodeValue'):
                                result.add_error(
                                    f"{item_prefix}: Channel Source Sequence item missing Code Value"
                                )
                            if not hasattr(source_item, 'CodingSchemeDesignator'):
                                result.add_error(
                                    f"{item_prefix}: Channel Source Sequence item missing Coding Scheme Designator"
                                )
    
    @staticmethod
    def _validate_timing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate timing-related attribute consistency."""
        
        # Validate trim values
        if hasattr(dataset, 'StartTrim') and hasattr(dataset, 'StopTrim'):
            try:
                start_trim = int(dataset.StartTrim)
                stop_trim = int(dataset.StopTrim)
                
                if start_trim < 1:
                    result.add_error("Start Trim (0008,2142) must be >= 1 (frame numbers are 1-based)")
                
                if stop_trim < 1:
                    result.add_error("Stop Trim (0008,2143) must be >= 1 (frame numbers are 1-based)")
                
                if start_trim > stop_trim:
                    result.add_error(
                        f"Start Trim ({start_trim}) cannot be greater than Stop Trim ({stop_trim})"
                    )
                    
            except (ValueError, TypeError):
                result.add_error("Start Trim and Stop Trim must be numeric")
        
        # Validate frame rates
        frame_rate_attrs = [
            ('RecommendedDisplayFrameRate', '0008,2144'),
            ('CineRate', '0018,0040')
        ]
        
        for attr_name, tag in frame_rate_attrs:
            if hasattr(dataset, attr_name):
                try:
                    rate = float(getattr(dataset, attr_name))
                    if rate <= 0:
                        result.add_error(f"{attr_name} ({tag}) must be positive")
                    elif rate > 1000:
                        result.add_warning(
                            f"{attr_name} ({tag}) value {rate} seems unusually high for frame rate"
                        )
                except (ValueError, TypeError):
                    result.add_error(f"{attr_name} ({tag}) must be numeric")
        
        # Validate duration values
        duration_attrs = [
            ('FrameDelay', '0018,1066'),
            ('ImageTriggerDelay', '0018,1067'),
            ('EffectiveDuration', '0018,0072'),
            ('ActualFrameDuration', '0018,1242')
        ]
        
        for attr_name, tag in duration_attrs:
            if hasattr(dataset, attr_name):
                try:
                    duration = float(getattr(dataset, attr_name))
                    if duration < 0:
                        result.add_error(f"{attr_name} ({tag}) must be non-negative")
                except (ValueError, TypeError):
                    result.add_error(f"{attr_name} ({tag}) must be numeric")
        
        # Cross-validation: Frame Time vs calculated rate
        if hasattr(dataset, 'FrameTime') and hasattr(dataset, 'CineRate'):
            try:
                frame_time = float(dataset.FrameTime)
                cine_rate = float(dataset.CineRate)
                
                if frame_time > 0 and cine_rate > 0:
                    calculated_rate = 1000.0 / frame_time  # Convert msec to frames/sec
                    rate_diff = abs(calculated_rate - cine_rate)
                    
                    if rate_diff > 0.1:  # Allow small tolerance
                        result.add_warning(
                            f"Frame Time ({frame_time} msec) and Cine Rate ({cine_rate} fps) are inconsistent. "
                            f"Frame Time suggests {calculated_rate:.2f} fps."
                        )
            except (ValueError, TypeError):
                pass  # Numeric validation already handled above
