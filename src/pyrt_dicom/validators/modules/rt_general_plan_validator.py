"""RT General Plan Module DICOM validation - PS3.3 C.8.8.9"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import PlanIntent, RTplanGeometry, RTPlanRelationship


class RTGeneralPlanValidator:
    """Validator for DICOM RT General Plan Module (PS3.3 C.8.8.9)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate RT General Plan Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTGeneralPlanValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTGeneralPlanValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTGeneralPlanValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Referenced Structure Set Sequence required if RT Plan Geometry is PATIENT
        rt_plan_geometry = getattr(dataset, 'RTPlanGeometry', '')
        if rt_plan_geometry == "PATIENT":
            if not hasattr(dataset, 'ReferencedStructureSetSequence'):
                result.add_error(
                    "Referenced Structure Set Sequence (300C,0060) is required when "
                    "RT Plan Geometry (300A,000C) is PATIENT"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # RT Plan Geometry (300A,000C)
        rt_plan_geometry = getattr(dataset, 'RTPlanGeometry', '')
        if rt_plan_geometry:
            valid_geometries = [geometry.value for geometry in RTplanGeometry]
            BaseValidator.validate_enumerated_value(
                rt_plan_geometry, valid_geometries,
                "RT Plan Geometry (300A,000C)", result
            )
        
        # Plan Intent (300A,000A)
        plan_intent = getattr(dataset, 'PlanIntent', '')
        if plan_intent:
            valid_intents = [intent.value for intent in PlanIntent]
            BaseValidator.validate_enumerated_value(
                plan_intent, valid_intents,
                "Plan Intent (300A,000A)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referenced Structure Set Sequence - each item needs SOP Class and Instance UIDs
        ref_structure_set_seq = getattr(dataset, 'ReferencedStructureSetSequence', [])
        for i, item in enumerate(ref_structure_set_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Structure Set Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Referenced RT Plan Sequence - each item needs SOP references and relationship
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, item in enumerate(ref_rt_plan_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
            
            # Validate RT Plan Relationship enumerated values
            rt_plan_relationship = item.get('RTPlanRelationship', '')
            if rt_plan_relationship:
                valid_relationships = [rel.value for rel in RTPlanRelationship]
                BaseValidator.validate_enumerated_value(
                    rt_plan_relationship, valid_relationships,
                    f"RT Plan Relationship (300A,0055) in item {i}", result
                )
        
        # Treatment Site Code Sequence - each item needs code value, scheme, and meaning
        treatment_site_code_seq = getattr(dataset, 'TreatmentSiteCodeSequence', [])
        for i, item in enumerate(treatment_site_code_seq):
            if not item.get('CodeValue'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i}: "
                    "Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i}: "
                    "Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result.add_error(
                    f"Treatment Site Code Sequence item {i}: "
                    "Code Meaning (0008,0104) is required"
                )
        
        # Referenced Dose Sequence - each item needs SOP Class and Instance UIDs
        ref_dose_seq = getattr(dataset, 'ReferencedDoseSequence', [])
        for i, item in enumerate(ref_dose_seq):
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced Dose Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced Dose Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # RT Assertions Sequence validation (basic structure check)
        rt_assertions_seq = getattr(dataset, 'RTAssertionsSequence', [])
        for i, item in enumerate(rt_assertions_seq):
            # Basic validation - specific assertion requirements would depend on the assertion type
            if not any(key in item for key in ['AssertionCodeSequence', 'AssertionUID']):
                result.add_warning(
                    f"RT Assertions Sequence item {i}: "
                    "Should contain assertion identification elements"
                )
