"""Structure Set Module DICOM validation - PS3.3 C.8.8.5"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import ROIGenerationAlgorithm


class StructureSetValidator:
    """Validator for DICOM Structure Set Module (PS3.3 C.8.8.5)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:
        """Validate Structure Set Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate enumerated values
        if config.check_enumerated_values:
            StructureSetValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            StructureSetValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Validate ROI Generation Algorithm in Structure Set ROI Sequence
        structure_set_roi_seq = getattr(dataset, 'StructureSetROISequence', [])
        for i, roi_item in enumerate(structure_set_roi_seq):
            roi_gen_algorithm = roi_item.get('ROIGenerationAlgorithm', '')
            if roi_gen_algorithm:
                valid_algorithms = [alg.value for alg in ROIGenerationAlgorithm]
                BaseValidator.validate_enumerated_value(
                    roi_gen_algorithm, valid_algorithms,
                    f"ROI Generation Algorithm (3006,0036) in ROI item {i}", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referenced Frame of Reference Sequence validation
        ref_frame_seq = getattr(dataset, 'ReferencedFrameOfReferenceSequence', [])
        for i, frame_item in enumerate(ref_frame_seq):
            # Frame of Reference UID is Type 1 within the sequence
            if not frame_item.get('FrameOfReferenceUID'):
                result.add_error(
                    f"Referenced Frame of Reference Sequence item {i}: "
                    "Frame of Reference UID (0020,0052) is required"
                )
            
            # Validate RT Referenced Study Sequence if present
            rt_ref_study_seq = frame_item.get('RTReferencedStudySequence', [])
            for j, study_item in enumerate(rt_ref_study_seq):
                if not study_item.get('ReferencedSOPClassUID'):
                    result.add_error(
                        f"RT Referenced Study Sequence item {j} in frame {i}: "
                        "Referenced SOP Class UID (0008,1150) is required"
                    )
                if not study_item.get('ReferencedSOPInstanceUID'):
                    result.add_error(
                        f"RT Referenced Study Sequence item {j} in frame {i}: "
                        "Referenced SOP Instance UID (0008,1155) is required"
                    )
                
                # Validate RT Referenced Series Sequence (Type 1 if study is present)
                rt_ref_series_seq = study_item.get('RTReferencedSeriesSequence', [])
                if not rt_ref_series_seq:
                    result.add_error(
                        f"RT Referenced Study Sequence item {j} in frame {i}: "
                        "RT Referenced Series Sequence (3006,0014) is required"
                    )
                
                for k, series_item in enumerate(rt_ref_series_seq):
                    if not series_item.get('SeriesInstanceUID'):
                        result.add_error(
                            f"RT Referenced Series Sequence item {k} in study {j}, frame {i}: "
                            "Series Instance UID (0020,000E) is required"
                        )
                    
                    # Validate Contour Image Sequence (Type 1 if series is present)
                    contour_image_seq = series_item.get('ContourImageSequence', [])
                    if not contour_image_seq:
                        result.add_error(
                            f"RT Referenced Series Sequence item {k} in study {j}, frame {i}: "
                            "Contour Image Sequence (3006,0016) is required"
                        )
                    
                    for l, image_item in enumerate(contour_image_seq):
                        if not image_item.get('ReferencedSOPClassUID'):
                            result.add_error(
                                f"Contour Image Sequence item {l} in series {k}, study {j}, frame {i}: "
                                "Referenced SOP Class UID (0008,1150) is required"
                            )
                        if not image_item.get('ReferencedSOPInstanceUID'):
                            result.add_error(
                                f"Contour Image Sequence item {l} in series {k}, study {j}, frame {i}: "
                                "Referenced SOP Instance UID (0008,1155) is required"
                            )
        
        # Structure Set ROI Sequence validation
        structure_set_roi_seq = getattr(dataset, 'StructureSetROISequence', [])
        for i, roi_item in enumerate(structure_set_roi_seq):
            # ROI Number is Type 1
            if not roi_item.get('ROINumber'):
                result.add_error(
                    f"Structure Set ROI Sequence item {i}: "
                    "ROI Number (3006,0022) is required"
                )
            
            # Referenced Frame of Reference UID is Type 1
            if not roi_item.get('ReferencedFrameOfReferenceUID'):
                result.add_error(
                    f"Structure Set ROI Sequence item {i}: "
                    "Referenced Frame of Reference UID (3006,0024) is required"
                )
            
            # ROI Name is Type 2 (required but may be empty)
            if 'ROIName' not in roi_item:
                result.add_error(
                    f"Structure Set ROI Sequence item {i}: "
                    "ROI Name (3006,0026) is required (Type 2)"
                )
            
            # ROI Generation Algorithm is Type 2 (required but may be empty)
            if 'ROIGenerationAlgorithm' not in roi_item:
                result.add_error(
                    f"Structure Set ROI Sequence item {i}: "
                    "ROI Generation Algorithm (3006,0036) is required (Type 2)"
                )
        
        # Source Series Information Sequence validation
        source_series_seq = getattr(dataset, 'SourceSeriesInformationSequence', [])
        for i, series_item in enumerate(source_series_seq):
            # Required elements for source series information
            required_fields = [
                ('Modality', '0008,0060'),
                ('SeriesDate', '0008,0021'),
                ('SeriesTime', '0008,0031'),
                ('SeriesDescription', '0008,103E'),
                ('SeriesInstanceUID', '0020,000E'),
                ('SeriesNumber', '0020,0011')
            ]
            
            for field_name, tag in required_fields:
                if not series_item.get(field_name):
                    result.add_error(
                        f"Source Series Information Sequence item {i}: "
                        f"{field_name} ({tag}) is required"
                    )
        
        # Predecessor Structure Set Sequence validation
        predecessor_seq = getattr(dataset, 'PredecessorStructureSetSequence', [])
        for i, pred_item in enumerate(predecessor_seq):
            if not pred_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Predecessor Structure Set Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not pred_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Predecessor Structure Set Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
