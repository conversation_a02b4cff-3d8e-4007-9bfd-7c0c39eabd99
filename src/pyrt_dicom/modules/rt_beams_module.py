"""
RT Beams Module - DICOM PS3.3 C.8.8.14

The RT Beams Module contains information defining equipment parameters 
for delivery of external radiation beams.

Note: This is a simplified implementation focusing on core functionality.
The full DICOM specification includes many additional sequences and attributes.
"""
from pydicom import Dataset
from typing import Dict, List
from .base_module import BaseModule
from ..enums.rt_enums import RTBeamLimitingDeviceType, PrimaryDosimeterUnit, EnhancedRTBeamLimitingDeviceDefinitionFlag
from ..enums import BeamType, RadiationType
from ..validators.modules.rt_beams_validator import RTBeamsValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class RTBeamsModule(BaseModule):
    """RT Beams Module implementation for DICOM PS3.3 C.8.8.14.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains information defining equipment parameters for external radiation beams.
    
    Note: This is a simplified implementation focusing on core functionality.
    
    Usage:
        # Create with required elements
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[
                beams.create_beam_item(
                    beam_number=1,
                    beam_type=BeamType.STATIC,
                    treatment_machine_name="TrueBeam",
                    radiation_type=RadiationType.PHOTON,
                    beam_limiting_device_sequence=[
                        beams.create_beam_limiting_device_item(
                            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                            number_of_leaf_jaw_pairs=1
                        )
                    ]
                )
            ]
        )
        
        # Validate
        result = beams.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        beam_sequence: list[dict[str, any]]
    ) -> 'RTBeamsModule':
        """Create RTBeamsModule from all required (Type 1) data elements.
        
        Args:
            beam_sequence (list[dict[str, any]]): Sequence of treatment beams (300A,00B0) Type 1
                
        Returns:
            RTBeamsModule: New module instance with required data elements set
        """
        instance = cls()
        instance.BeamSequence = beam_sequence
        return instance
    
    def with_optional_elements(
        self
    ) -> 'RTBeamsModule':
        """Add optional (Type 3) elements.
        
        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the Beam Sequence.
        
        Returns:
            RTBeamsModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_beam_item(
        beam_number: int,
        beam_type: str | BeamType,
        treatment_machine_name: str = "",
        radiation_type: str | RadiationType = "",
        number_of_wedges: int = 0,
        number_of_compensators: int = 0,
        number_of_boli: int = 0,
        number_of_blocks: int = 0,
        number_of_control_points: int = 2,
        final_cumulative_meterset_weight: float | None = None,
        control_point_sequence: list[dict[str, any]] | None = None,
        beam_name: str | None = None,
        beam_description: str | None = None,
        entity_long_label: str | None = None,
        primary_dosimeter_unit: str | PrimaryDosimeterUnit | None = None,
        source_axis_distance: float | None = None,
        enhanced_rt_beam_limiting_device_definition_flag: str | EnhancedRTBeamLimitingDeviceDefinitionFlag | None = None,
        beam_limiting_device_sequence: list[dict[str, any]] | None = None,
        enhanced_rt_beam_limiting_device_sequence: list[dict[str, any]] | None = None,
        wedge_sequence: list[dict[str, any]] | None = None,
        compensator_sequence: list[dict[str, any]] | None = None,
        referenced_bolus_sequence: list[dict[str, any]] | None = None,
        block_sequence: list[dict[str, any]] | None = None,
        referenced_patient_setup_number: int | None = None,
        referenced_tolerance_table_number: int | None = None,
        manufacturer: str | None = None,
        institution_name: str | None = None,
        institution_address: str | None = None
    ) -> Dataset:
        """Create beam sequence item.

        Args:
            beam_number (int): Identification number of the Beam (300A,00C0) Type 1
            beam_type (str | BeamType): Motion characteristic of Beam (300A,00C4) Type 1
            treatment_machine_name (str): Name of treatment machine (300A,00B2) Type 2
            radiation_type (str | RadiationType): Particle type of Beam (300A,00C6) Type 2
            number_of_wedges (int): Number of wedges (300A,00D0) Type 1
            number_of_compensators (int): Number of compensators (300A,00E0) Type 1
            number_of_boli (int): Number of boli (300A,00ED) Type 1
            number_of_blocks (int): Number of blocks (300A,00F0) Type 1
            number_of_control_points (int): Number of control points (300A,0110) Type 1
            final_cumulative_meterset_weight (float | None): Final meterset weight (300A,010E) Type 1C
            control_point_sequence (list[dict[str, any]] | None): Control points (300A,0111) Type 1
            beam_name (str | None): User-defined name for Beam (300A,00C2) Type 3
            beam_description (str | None): User-defined description for Beam (300A,00C3) Type 3
            entity_long_label (str | None): User-defined label for Beam (3010,0038) Type 3
            primary_dosimeter_unit (str | PrimaryDosimeterUnit | None): Measurement unit (300A,00B3) Type 3
            source_axis_distance (float | None): Source to axis distance (300A,00B4) Type 3
            enhanced_rt_beam_limiting_device_definition_flag (str | EnhancedRTBeamLimitingDeviceDefinitionFlag | None): Enhanced device flag (3008,00A3) Type 3
            beam_limiting_device_sequence (list[dict[str, any]] | None): Beam limiting devices (300A,00B6) Type 1C
            enhanced_rt_beam_limiting_device_sequence (list[dict[str, any]] | None): Enhanced devices (3008,00A1) Type 1C
            wedge_sequence (list[dict[str, any]] | None): Wedges (300A,00D1) Type 1C
            compensator_sequence (list[dict[str, any]] | None): Compensators (300A,00E3) Type 1C
            referenced_bolus_sequence (list[dict[str, any]] | None): Boli (300C,00B0) Type 1C
            block_sequence (list[dict[str, any]] | None): Blocks (300A,00F4) Type 1C
            referenced_patient_setup_number (int | None): Patient setup to use (300C,006A) Type 3
            referenced_tolerance_table_number (int | None): Tolerance table to use (300C,00A0) Type 3
            manufacturer (str | None): Equipment manufacturer (0008,0070) Type 3
            institution_name (str | None): Institution name (0008,0080) Type 3
            institution_address (str | None): Institution address (0008,0081) Type 3

        Returns:
            Dataset: Beam sequence item
        """
        # Validate conditional requirements
        enhanced_flag = enhanced_rt_beam_limiting_device_definition_flag
        if enhanced_flag is None or (hasattr(enhanced_flag, 'value') and enhanced_flag.value == 'NO') or enhanced_flag == 'NO':
            if beam_limiting_device_sequence is None:
                raise ValueError("Beam Limiting Device Sequence is required when Enhanced RT Beam Limiting Device Definition Flag is absent or NO")
        
        if enhanced_flag is not None and ((hasattr(enhanced_flag, 'value') and enhanced_flag.value == 'YES') or enhanced_flag == 'YES'):
            if enhanced_rt_beam_limiting_device_sequence is None:
                raise ValueError("Enhanced RT Beam Limiting Device Sequence is required when Enhanced RT Beam Limiting Device Definition Flag is YES")

        item = Dataset()
        item.BeamNumber = beam_number
        item.BeamType = beam_type.value if hasattr(beam_type, 'value') else str(beam_type)
        item.TreatmentMachineName = treatment_machine_name
        item.RadiationType = radiation_type.value if hasattr(radiation_type, 'value') else str(radiation_type)
        item.NumberOfWedges = number_of_wedges
        item.NumberOfCompensators = number_of_compensators
        item.NumberOfBoli = number_of_boli
        item.NumberOfBlocks = number_of_blocks
        item.NumberOfControlPoints = number_of_control_points

        # Add Type 1C elements
        if final_cumulative_meterset_weight is not None:
            item.FinalCumulativeMetersetWeight = final_cumulative_meterset_weight
        if control_point_sequence is not None:
            item.ControlPointSequence = control_point_sequence

        # Add conditional elements
        if beam_limiting_device_sequence is not None:
            item.BeamLimitingDeviceSequence = beam_limiting_device_sequence
        if enhanced_rt_beam_limiting_device_sequence is not None:
            item.EnhancedRTBeamLimitingDeviceSequence = enhanced_rt_beam_limiting_device_sequence
        
        # Add optional elements if provided
        if beam_name is not None:
            item.BeamName = beam_name
        if beam_description is not None:
            item.BeamDescription = beam_description
        if entity_long_label is not None:
            item.EntityLongLabel = entity_long_label
        if primary_dosimeter_unit is not None:
            item.PrimaryDosimeterUnit = primary_dosimeter_unit.value if hasattr(primary_dosimeter_unit, 'value') else str(primary_dosimeter_unit)
        if source_axis_distance is not None:
            item.SourceAxisDistance = source_axis_distance
        if enhanced_rt_beam_limiting_device_definition_flag is not None:
            item.EnhancedRTBeamLimitingDeviceDefinitionFlag = enhanced_rt_beam_limiting_device_definition_flag.value if hasattr(enhanced_rt_beam_limiting_device_definition_flag, 'value') else str(enhanced_rt_beam_limiting_device_definition_flag)
        if wedge_sequence is not None:
            item.WedgeSequence = wedge_sequence
        if compensator_sequence is not None:
            item.CompensatorSequence = compensator_sequence
        if referenced_bolus_sequence is not None:
            item.ReferencedBolusSequence = referenced_bolus_sequence
        if block_sequence is not None:
            item.BlockSequence = block_sequence
        if referenced_patient_setup_number is not None:
            item.ReferencedPatientSetupNumber = referenced_patient_setup_number
        if referenced_tolerance_table_number is not None:
            item.ReferencedToleranceTableNumber = referenced_tolerance_table_number
        if manufacturer is not None:
            item.Manufacturer = manufacturer
        if institution_name is not None:
            item.InstitutionName = institution_name
        if institution_address is not None:
            item.InstitutionAddress = institution_address

        return item
    
    @staticmethod
    def create_beam_limiting_device_item(
        rt_beam_limiting_device_type: str | RTBeamLimitingDeviceType,
        number_of_leaf_jaw_pairs: int,
        source_to_beam_limiting_device_distance: float | None = None,
        leaf_position_boundaries: list[float] | None = None
    ) -> Dataset:
        """Create beam limiting device sequence item.

        Args:
            rt_beam_limiting_device_type (str | RTBeamLimitingDeviceType): Type of device (300A,00B8) Type 1
            number_of_leaf_jaw_pairs (int): Number of leaf/jaw pairs (300A,00BC) Type 1
            source_to_beam_limiting_device_distance (float | None): Source to device distance (300A,00BA) Type 3
            leaf_position_boundaries (list[float] | None): Leaf position boundaries (300A,00BE) Type 2C

        Returns:
            Dataset: Beam limiting device sequence item
        """
        device_type_str = rt_beam_limiting_device_type.value if hasattr(rt_beam_limiting_device_type, 'value') else str(rt_beam_limiting_device_type)

        # Validate conditional requirements
        if device_type_str in ['MLCX', 'MLCY'] and leaf_position_boundaries is None:
            raise ValueError("Leaf Position Boundaries is required when RT Beam Limiting Device Type is MLCX or MLCY")

        item = Dataset()
        item.RTBeamLimitingDeviceType = device_type_str
        item.NumberOfLeafJawPairs = number_of_leaf_jaw_pairs

        # Add conditional elements
        if leaf_position_boundaries is not None:
            item.LeafPositionBoundaries = leaf_position_boundaries

        # Add optional elements if provided
        if source_to_beam_limiting_device_distance is not None:
            item.SourceToBeamLimitingDeviceDistance = source_to_beam_limiting_device_distance

        return item

    @staticmethod
    def create_control_point_item(
        control_point_index: int,
        cumulative_meterset_weight: float = 0.0,
        gantry_angle: float | None = None,
        gantry_rotation_direction: str | None = None,
        beam_limiting_device_angle: float | None = None,
        beam_limiting_device_rotation_direction: str | None = None,
        patient_support_angle: float | None = None,
        patient_support_rotation_direction: str | None = None,
        table_top_eccentric_angle: float | None = None,
        table_top_eccentric_rotation_direction: str | None = None,
        table_top_vertical_position: float | None = None,
        table_top_longitudinal_position: float | None = None,
        table_top_lateral_position: float | None = None,
        isocenter_position: list[float] | None = None,
        beam_limiting_device_position_sequence: list[dict[str, any]] | None = None,
        nominal_beam_energy: float | None = None,
        dose_rate_set: float | None = None
    ) -> Dataset:
        """Create control point sequence item.

        Args:
            control_point_index (int): Index of control point (300A,0112) Type 1
            cumulative_meterset_weight (float): Cumulative weight (300A,0134) Type 2
            gantry_angle (float | None): Gantry angle in degrees (300A,011E) Type 1C
            gantry_rotation_direction (str | None): Gantry rotation direction (300A,011F) Type 1C
            beam_limiting_device_angle (float | None): Collimator angle (300A,0120) Type 1C
            beam_limiting_device_rotation_direction (str | None): Collimator rotation direction (300A,0121) Type 1C
            patient_support_angle (float | None): Table angle (300A,0122) Type 1C
            patient_support_rotation_direction (str | None): Table rotation direction (300A,0123) Type 1C
            table_top_eccentric_angle (float | None): Table eccentric angle (300A,0125) Type 1C
            table_top_eccentric_rotation_direction (str | None): Table eccentric rotation direction (300A,0126) Type 1C
            table_top_vertical_position (float | None): Table vertical position (300A,0128) Type 2C
            table_top_longitudinal_position (float | None): Table longitudinal position (300A,0129) Type 2C
            table_top_lateral_position (float | None): Table lateral position (300A,012A) Type 2C
            isocenter_position (list[float] | None): Isocenter coordinates (300A,012C) Type 2C
            beam_limiting_device_position_sequence (list[dict[str, any]] | None): Device positions (300A,011A) Type 1C
            nominal_beam_energy (float | None): Beam energy (300A,0114) Type 3
            dose_rate_set (float | None): Dose rate (300A,0115) Type 3

        Returns:
            Dataset: Control point sequence item
        """
        item = Dataset()
        item.ControlPointIndex = control_point_index
        item.CumulativeMetersetWeight = cumulative_meterset_weight

        # Add conditional and optional elements if provided
        if gantry_angle is not None:
            item.GantryAngle = gantry_angle
        if gantry_rotation_direction is not None:
            item.GantryRotationDirection = gantry_rotation_direction
        if beam_limiting_device_angle is not None:
            item.BeamLimitingDeviceAngle = beam_limiting_device_angle
        if beam_limiting_device_rotation_direction is not None:
            item.BeamLimitingDeviceRotationDirection = beam_limiting_device_rotation_direction
        if patient_support_angle is not None:
            item.PatientSupportAngle = patient_support_angle
        if patient_support_rotation_direction is not None:
            item.PatientSupportRotationDirection = patient_support_rotation_direction
        if table_top_eccentric_angle is not None:
            item.TableTopEccentricAngle = table_top_eccentric_angle
        if table_top_eccentric_rotation_direction is not None:
            item.TableTopEccentricRotationDirection = table_top_eccentric_rotation_direction
        if table_top_vertical_position is not None:
            item.TableTopVerticalPosition = table_top_vertical_position
        if table_top_longitudinal_position is not None:
            item.TableTopLongitudinalPosition = table_top_longitudinal_position
        if table_top_lateral_position is not None:
            item.TableTopLateralPosition = table_top_lateral_position
        if isocenter_position is not None:
            item.IsocenterPosition = isocenter_position
        if beam_limiting_device_position_sequence is not None:
            item.BeamLimitingDevicePositionSequence = beam_limiting_device_position_sequence
        if nominal_beam_energy is not None:
            item.NominalBeamEnergy = nominal_beam_energy
        if dose_rate_set is not None:
            item.DoseRateSet = dose_rate_set

        return item

    @staticmethod
    def create_beam_limiting_device_position_item(
        rt_beam_limiting_device_type: str | RTBeamLimitingDeviceType,
        leaf_jaw_positions: list[float]
    ) -> Dataset:
        """Create beam limiting device position sequence item.

        Args:
            rt_beam_limiting_device_type (str | RTBeamLimitingDeviceType): Device type (300A,00B8) Type 1
            leaf_jaw_positions (list[float]): Leaf/jaw positions in mm (300A,011C) Type 1

        Returns:
            Dataset: Beam limiting device position sequence item
        """
        item = Dataset()
        item.RTBeamLimitingDeviceType = rt_beam_limiting_device_type.value if hasattr(rt_beam_limiting_device_type, 'value') else str(rt_beam_limiting_device_type)
        item.LeafJawPositions = leaf_jaw_positions
        return item

    @staticmethod
    def create_wedge_item(
        wedge_number: int,
        wedge_type: str = "",
        wedge_angle: float = 0.0,
        wedge_factor: float = 1.0,
        wedge_orientation: float = 0.0,
        wedge_id: str | None = None,
        accessory_code: str | None = None,
        source_to_wedge_tray_distance: float | None = None,
        effective_wedge_angle: float | None = None
    ) -> Dataset:
        """Create wedge sequence item.

        Args:
            wedge_number (int): Wedge identification number (300A,00D2) Type 1
            wedge_type (str): Type of wedge (300A,00D3) Type 2
            wedge_angle (float): Nominal wedge angle (300A,00D5) Type 2
            wedge_factor (float): Nominal wedge factor (300A,00D6) Type 2
            wedge_orientation (float): Wedge orientation (300A,00D8) Type 2
            wedge_id (str | None): User identifier for wedge (300A,00D4) Type 3
            accessory_code (str | None): Bar-code identifier (300A,00F9) Type 3
            source_to_wedge_tray_distance (float | None): Source to tray distance (300A,00DA) Type 3
            effective_wedge_angle (float | None): Effective wedge angle (300A,00DE) Type 3

        Returns:
            Dataset: Wedge sequence item
        """
        item = Dataset()
        item.WedgeNumber = wedge_number
        item.WedgeType = wedge_type
        item.WedgeAngle = wedge_angle
        item.WedgeFactor = wedge_factor
        item.WedgeOrientation = wedge_orientation

        # Add optional elements if provided
        if wedge_id is not None:
            item.WedgeID = wedge_id
        if accessory_code is not None:
            item.AccessoryCode = accessory_code
        if source_to_wedge_tray_distance is not None:
            item.SourceToWedgeTrayDistance = source_to_wedge_tray_distance
        if effective_wedge_angle is not None:
            item.EffectiveWedgeAngle = effective_wedge_angle

        return item

    @property
    def has_beams(self) -> bool:
        """Check if beam data is present.
        
        Returns:
            bool: True if Beam Sequence is present
        """
        return hasattr(self, 'BeamSequence')
    
    @property
    def beam_count(self) -> int:
        """Get the number of beams in this module.
        
        Returns:
            int: Number of beams in Beam Sequence
        """
        beam_sequence = getattr(self, 'BeamSequence', [])
        return len(beam_sequence)
    
    def get_beam_numbers(self) -> list[int]:
        """Get list of beam numbers present in this module.
        
        Returns:
            list[int]: List of beam numbers
        """
        beam_sequence = getattr(self, 'BeamSequence', [])
        beam_numbers = []
        for beam_item in beam_sequence:
            beam_number = beam_item.get('BeamNumber')
            if beam_number is not None:
                beam_numbers.append(beam_number)
        return beam_numbers
    
    def get_beam_by_number(self, beam_number: int) -> dict[str, any] | None:
        """Get beam by its number.
        
        Args:
            beam_number (int): Beam number to find
            
        Returns:
            dict[str, any] | None: Beam item or None if not found
        """
        beam_sequence = getattr(self, 'BeamSequence', [])
        for beam_item in beam_sequence:
            if beam_item.get('BeamNumber') == beam_number:
                return beam_item
        return None
    
    def get_radiation_types(self) -> list[str]:
        """Get list of radiation types present in this module.
        
        Returns:
            list[str]: List of unique radiation types
        """
        beam_sequence = getattr(self, 'BeamSequence', [])
        radiation_types = []
        for beam_item in beam_sequence:
            radiation_type = beam_item.get('RadiationType', '')
            if radiation_type and radiation_type not in radiation_types:
                radiation_types.append(radiation_type)
        return radiation_types
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate this RT Beams Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTBeamsValidator.validate(self, config)
