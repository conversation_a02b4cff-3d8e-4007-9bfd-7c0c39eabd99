"""
Modality LUT Module - DICOM PS3.3 C.11.1

The Modality LUT Module describes the Modality LUT transformation that converts
manufacturer dependent pixel values to pixel values which are meaningful for
the application.
"""
from typing import Optional, Union, List, Dict, Any
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.image_enums import ModalityLutType, RescaleType
from ..validators.modules.modality_lut_validator import ModalityLutValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class ModalityLutModule(BaseModule):
    """Modality LUT Module implementation for DICOM PS3.3 C.11.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes the Modality LUT transformation that converts manufacturer dependent
    pixel values to pixel values which are meaningful for the application.
    
    Either a Modality LUT Sequence containing a single Item or Rescale Slope and 
    Intercept values shall be present but not both.
    
    Usage:
        # Create with Modality LUT Sequence (from_required_elements is not needed - no required elements)
        modality_lut = ModalityLutModule().with_modality_lut_sequence(
            modality_lut_sequence=[
                ModalityLutModule.create_modality_lut_item(
                    lut_descriptor=[256, 0, 8],
                    modality_lut_type=ModalityLutType.HU,
                    lut_data=[0, 1, 2, 3]  # Example LUT data
                )
            ]
        )
        
        # Or create with Rescale parameters
        modality_lut = ModalityLutModule().with_rescale_parameters(
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            rescale_type=RescaleType.HU
        )
        
        # Validate
        result = modality_lut.validate()
    """
    
    @classmethod
    def from_required_elements(cls) -> 'ModalityLutModule':
        """Create module with required elements.
        
        Note: This module has no Type 1 or Type 2 elements - all elements are conditional.
        Either Modality LUT Sequence or Rescale parameters must be provided via conditional methods.
        
        Returns:
            ModalityLutModule: New module instance
        """
        return cls()
    
    def with_optional_elements(self, **kwargs) -> 'ModalityLutModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Modality LUT Module has no Type 3 elements defined in DICOM PS3.3 C.11.1.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            ModalityLutModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"ModalityLutModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    def with_modality_lut_sequence(
        self,
        modality_lut_sequence: List[Dict[str, Any]]
    ) -> 'ModalityLutModule':
        """Add Modality LUT Sequence (Type 1C).
        
        Required if Rescale Intercept is not present. Only a single Item shall be included.
        
        Args:
            modality_lut_sequence: List containing single modality LUT item
            
        Returns:
            ModalityLutModule: Self for method chaining
        """
        if len(modality_lut_sequence) != 1:
            raise ValueError("Modality LUT Sequence must contain exactly one item")
        
        self.ModalityLUTSequence = modality_lut_sequence
        return self
    
    def with_rescale_parameters(
        self,
        rescale_intercept: float,
        rescale_slope: float,
        rescale_type: Union[RescaleType, str]
    ) -> 'ModalityLutModule':
        """Add Rescale parameters (Type 1C).
        
        Required if Modality LUT Sequence is not present. All three parameters
        are required together.
        
        Args:
            rescale_intercept: The value b in relationship between stored values and output units
            rescale_slope: The value m in the equation specified by Rescale Intercept
            rescale_type: Specifies the output units of Rescale Slope and Intercept
            
        Returns:
            ModalityLutModule: Self for method chaining
        """
        self.RescaleIntercept = str(rescale_intercept)
        self.RescaleSlope = str(rescale_slope)
        self.RescaleType = self._format_enum_value(rescale_type)
        return self
    
    @staticmethod
    def create_modality_lut_item(
        lut_descriptor: List[int],
        modality_lut_type: Union[ModalityLutType, str],
        lut_data: List[int],
        lut_explanation: Optional[str] = None
    ) -> Dataset:
        """Create a Modality LUT Sequence item.

        Args:
            lut_descriptor: Format of the LUT Data [entries, first_value, bits_per_entry]
            modality_lut_type: Specifies the output values of this Modality LUT
            lut_data: LUT Data values
            lut_explanation: Optional free form text explanation of the LUT meaning

        Returns:
            Dataset representing a Modality LUT Sequence item
        """
        item = Dataset()
        item.LUTDescriptor = lut_descriptor
        item.ModalityLUTType = modality_lut_type.value if hasattr(modality_lut_type, 'value') else str(modality_lut_type)
        item.LUTData = lut_data

        if lut_explanation is not None:
            item.LUTExplanation = lut_explanation

        return item
    
    @property
    def has_modality_lut_sequence(self) -> bool:
        """Check if Modality LUT Sequence is present.
        
        Returns:
            bool: True if Modality LUT Sequence is present
        """
        return hasattr(self, 'ModalityLUTSequence')
    
    @property
    def has_rescale_parameters(self) -> bool:
        """Check if Rescale parameters are present.
        
        Returns:
            bool: True if all rescale parameters are present
        """
        return (hasattr(self, 'RescaleIntercept') and 
                hasattr(self, 'RescaleSlope') and 
                hasattr(self, 'RescaleType'))
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if either LUT sequence or rescale parameters are present
        """
        return self.has_modality_lut_sequence or self.has_rescale_parameters
    
    def validate(self, config: Optional[ValidationConfig] = None) -> ValidationResult:
        """Validate this Modality LUT Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        return ModalityLutValidator.validate(self, config)
