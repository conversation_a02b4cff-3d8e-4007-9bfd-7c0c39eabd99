"""
Multi-frame Module - DICOM PS3.3 C.7.6.6

The Multi-frame Module describes a Multi-frame pixel data Image.
"""
from typing import Union, List
from pydicom.tag import Tag, BaseTag
from .base_module import BaseModule
from ..enums.image_enums import StereoPairsPresent
from ..validators.modules.multi_frame_validator import MultiFrameValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class MultiFrameModule(BaseModule):
    """Multi-frame Module implementation for DICOM PS3.3 C.7.6.6.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes a Multi-frame pixel data Image.
    
    Usage:
        # Create with required elements using static helper
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )
        
        # Or create directly with string format (will be converted)
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=["0018,1063"]  # Will be converted to proper AT format
        )
        
        # Add optional elements
        multi_frame.with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO,
            encapsulated_pixel_data_value_total_length=1024000
        )
        
        # Validate
        result = multi_frame.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        number_of_frames: int,
        frame_increment_pointer: List[Union[int, BaseTag, str]]
    ) -> 'MultiFrameModule':
        """Create MultiFrameModule from all required (Type 1) data elements.
        
        Args:
            number_of_frames (int): Number of frames in multi-frame image (0028,0008) Type 1.
                Must be greater than zero.
            frame_increment_pointer (list[int | Tag | str]): Data element tags for frame sequencing (0028,0009) Type 1.
                List of DICOM tags that point to attributes used as frame increment in multi-frame pixel data.
                Can be provided as:
                - int: Combined tag value (e.g., 0x00181063)
                - Tag: pydicom Tag object (e.g., Tag(0x0018, 0x1063))  
                - str: Tag in format "GGGG,EEEE" (e.g., "0018,1063") - will be converted to int
            
        Returns:
            MultiFrameModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.NumberOfFrames = number_of_frames
        
        # Convert frame increment pointer values to proper integer format for AT VR
        converted_tags = []
        for tag in frame_increment_pointer:
            if isinstance(tag, str):
                # Convert "GGGG,EEEE" format to integer
                if ',' in tag:
                    group_str, element_str = tag.split(',')
                    group = int(group_str, 16)
                    element = int(element_str, 16)
                    converted_tags.append((group << 16) | element)
                else:
                    # Assume it's a hex string like "00181063"
                    converted_tags.append(int(tag, 16))
            elif isinstance(tag, BaseTag):
                # Convert Tag object to integer
                converted_tags.append(int(tag))
            elif isinstance(tag, int):
                # Already in correct format
                converted_tags.append(tag)
            else:
                raise ValueError(f"Invalid frame increment pointer format: {tag}")
        
        instance.FrameIncrementPointer = converted_tags
        return instance
    
    def with_optional_elements(
        self,
        stereo_pairs_present: str | StereoPairsPresent | None = None,
        encapsulated_pixel_data_value_total_length: int | None = None
    ) -> 'MultiFrameModule':
        """Add optional (Type 3) data elements.
        
        Args:
            stereo_pairs_present (str | StereoPairsPresent | None): Stereoscopic pairs flag (0022,0028) Type 3.
                Indicates if multi-frame pixel data consists of left and right stereoscopic pairs.
            encapsulated_pixel_data_value_total_length (int | None): Total pixel data length (7FE0,0003) Type 3.
                Length of pixel data bit stream in bytes when all fragments are combined.
            
        Returns:
            MultiFrameModule: Self with optional elements added
        """
        if stereo_pairs_present is not None:
            self.StereoPairsPresent = self._format_enum_value(stereo_pairs_present)
        self._set_attribute_if_not_none('EncapsulatedPixelDataValueTotalLength', encapsulated_pixel_data_value_total_length)
        return self
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time() -> List[int]:
        """Create frame increment pointer for Frame Time attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time (0018,1063)
        """
        return [int(Tag(0x0018, 0x1063))]
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time_vector() -> List[int]:
        """Create frame increment pointer for Frame Time Vector attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time Vector (0018,1065)
        """
        return [int(Tag(0x0018, 0x1065))]
    
    @staticmethod
    def create_frame_increment_pointer_for_functional_groups() -> List[int]:
        """Create frame increment pointer for Per-Frame Functional Groups.
        
        Returns:
            list[int]: Frame increment pointer pointing to Per-Frame Functional Groups Sequence (5200,9230)
        """
        return [int(Tag(0x5200, 0x9230))]
    
    @property
    def is_single_frame(self) -> bool:
        """Check if this is actually a single frame (edge case)."""
        return hasattr(self, 'NumberOfFrames') and self.NumberOfFrames == 1
    
    @property
    def is_multi_frame(self) -> bool:
        """Check if this is truly multi-frame."""
        return hasattr(self, 'NumberOfFrames') and self.NumberOfFrames > 1
    
    @property
    def has_stereo_pairs(self) -> bool:
        """Check if stereoscopic pairs are present."""
        return hasattr(self, 'StereoPairsPresent') and self.StereoPairsPresent == "YES"
    
    @property
    def expected_stereo_frame_count(self) -> int | None:
        """Calculate expected number of stereo frame pairs."""
        if not self.has_stereo_pairs or not hasattr(self, 'NumberOfFrames'):
            return None
        # For stereo pairs, frames come in pairs (left/right)
        return self.NumberOfFrames // 2
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        frame_time_tag = Tag(0x0018, 0x1063)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_tag in fip
        else:
            return fip == frame_time_tag
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        frame_time_vector_tag = Tag(0x0018, 0x1065)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_vector_tag in fip
        else:
            return fip == frame_time_vector_tag
    
    @property
    def uses_functional_groups(self) -> bool:
        """Check if Per-Frame Functional Groups are used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        functional_groups_tag = Tag(0x5200, 0x9230)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return functional_groups_tag in fip
        else:
            return fip == functional_groups_tag
    
    def get_frame_increment_attributes(self) -> List[str]:
        """Get list of frame increment attribute names.
        
        Returns:
            list[str]: List of DICOM attribute names referenced by frame increment pointer
        """
        if not hasattr(self, 'FrameIncrementPointer'):
            return []
        
        # Map common tags to attribute names
        tag_to_attr = {
            Tag(0x0018, 0x1063): "FrameTime",
            Tag(0x0018, 0x1065): "FrameTimeVector", 
            Tag(0x5200, 0x9230): "PerFrameFunctionalGroupsSequence"
        }
        
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            tag_list = fip
        else:
            tag_list = [fip]
        
        attributes = []
        for tag in tag_list:
            if tag in tag_to_attr:
                attributes.append(tag_to_attr[tag])
            else:
                # For unknown tags, convert Tag back to hex format
                if hasattr(tag, 'group') and hasattr(tag, 'element'):
                    attributes.append(f"Tag{tag.group:04X}{tag.element:04X}")
                else:
                    attributes.append(f"UnknownTag_{tag}")
        
        return attributes
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate module data against DICOM standard."""
        return MultiFrameValidator.validate(self, config)
