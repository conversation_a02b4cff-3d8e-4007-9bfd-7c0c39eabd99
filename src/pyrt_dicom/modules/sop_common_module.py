"""
SOP Common Module - DICOM PS3.3 C.12.1

The SOP Common Module contains attributes that are required for proper 
functioning and identification of the associated SOP Instances. They do not 
specify any semantics about the Real-World Object represented by the IOD.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView, 
    ContentQualification, LongitudinalTemporalInformationModified
)
from ..validators.modules.sop_common_validator import SOPCommonValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class SOPCommonModule(BaseModule):
    """SOP Common Module implementation for DICOM PS3.3 C.12.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes required for proper functioning and identification 
    of the associated SOP Instances.
    
    Usage:
        # Create with required elements
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        # Add optional elements
        sop.with_optional_elements(
            instance_creation_date="20240101",
            instance_creation_time="120000",
            instance_creator_uid="1.2.3.4.5.6.7.8.10",
            synthetic_data=SyntheticData.NO
        )
        
        # Add conditional character set if needed
        sop.with_specific_character_set("ISO_IR 100")
        
        # Validate
        result = sop.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        sop_class_uid: str,
        sop_instance_uid: str
    ) -> 'SOPCommonModule':
        """Create SOPCommonModule from all required (Type 1) data elements.
        
        Args:
            sop_class_uid (str): Uniquely identifies the SOP Class (0008,0016) Type 1
            sop_instance_uid (str): Uniquely identifies the SOP Instance (0008,0018) Type 1
                
        Returns:
            SOPCommonModule: New module instance with required data elements set
        """
        instance = cls()
        instance.SOPClassUID = sop_class_uid
        instance.SOPInstanceUID = sop_instance_uid
        return instance
    
    def with_specific_character_set(
        self,
        specific_character_set: str | list[str]
    ) -> 'SOPCommonModule':
        """Add specific character set.
        
        Note: Specific Character Set (0008,0005) is Type 1C - required if an 
        expanded or replacement character set is used.
        
        Args:
            specific_character_set (str | list[str]): Character set that expands or replaces Basic Graphic Set (0008,0005) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.SpecificCharacterSet = specific_character_set
        return self
    
    def with_optional_elements(
        self,
        instance_creation_date: str | datetime | date | None = None,
        instance_creation_time: str | datetime | None = None,
        instance_coercion_datetime: str | datetime | None = None,
        instance_creator_uid: str | None = None,
        related_general_sop_class_uid: str | None = None,
        original_specialized_sop_class_uid: str | None = None,
        synthetic_data: str | SyntheticData | None = None,
        coding_scheme_identification_sequence: list[dict[str, any]] | None = None,
        context_group_identification_sequence: list[dict[str, any]] | None = None,
        mapping_resource_identification_sequence: list[dict[str, any]] | None = None,
        timezone_offset_from_utc: str | None = None,
        contributing_equipment_sequence: list[dict[str, any]] | None = None,
        instance_number: int | None = None,
        sop_instance_status: str | SOPInstanceStatus | None = None,
        sop_authorization_datetime: str | datetime | None = None,
        sop_authorization_comment: str | None = None,
        authorization_equipment_certification_number: str | None = None
    ) -> 'SOPCommonModule':
        """Add optional (Type 3) elements.
        
        Args:
            instance_creation_date (str | datetime | date | None): Date SOP Instance was created (0008,0012) Type 3
            instance_creation_time (str | datetime | None): Time SOP Instance was created (0008,0013) Type 3
            instance_coercion_datetime (str | datetime | None): Date/time instance was last coerced (0008,0015) Type 3
            instance_creator_uid (str | None): Uniquely identifies device that created instance (0008,0014) Type 3
            related_general_sop_class_uid (str | None): Related General SOP Class UID (0008,001A) Type 3
            original_specialized_sop_class_uid (str | None): Original Specialized SOP Class UID (0008,001B) Type 3
            synthetic_data (str | SyntheticData | None): Whether content was made artificially (0008,001C) Type 3
            coding_scheme_identification_sequence (list[dict] | None): Coding scheme identification (0008,0110) Type 3
            context_group_identification_sequence (list[dict] | None): Context group identification (0008,0123) Type 3
            mapping_resource_identification_sequence (list[dict] | None): Mapping resource identification (0008,0124) Type 3
            timezone_offset_from_utc (str | None): Offset from UTC to timezone (0008,0201) Type 3
            contributing_equipment_sequence (list[dict] | None): Contributing equipment (0018,A001) Type 3
            instance_number (int | None): Number that identifies this instance (0020,0013) Type 3
            sop_instance_status (str | SOPInstanceStatus | None): Storage status flag (0100,0410) Type 3
            sop_authorization_datetime (str | datetime | None): Date/time status set to AO (0100,0420) Type 3
            sop_authorization_comment (str | None): Comments for status AO (0100,0424) Type 3
            authorization_equipment_certification_number (str | None): Certification number (0100,0426) Type 3
        """
        if instance_creation_date is not None:
            self.InstanceCreationDate = self._format_date_value(instance_creation_date)
        if instance_creation_time is not None:
            self.InstanceCreationTime = self._format_time_value(instance_creation_time)
        if instance_coercion_datetime is not None:
            self.InstanceCoercionDateTime = instance_coercion_datetime
        
        self._set_attribute_if_not_none('InstanceCreatorUID', instance_creator_uid)
        self._set_attribute_if_not_none('RelatedGeneralSOPClassUID', related_general_sop_class_uid)
        self._set_attribute_if_not_none('OriginalSpecializedSOPClassUID', original_specialized_sop_class_uid)
        
        if synthetic_data is not None:
            self.SyntheticData = self._format_enum_value(synthetic_data)
        
        self._set_attribute_if_not_none('CodingSchemeIdentificationSequence', coding_scheme_identification_sequence)
        self._set_attribute_if_not_none('ContextGroupIdentificationSequence', context_group_identification_sequence)
        self._set_attribute_if_not_none('MappingResourceIdentificationSequence', mapping_resource_identification_sequence)
        self._set_attribute_if_not_none('TimezoneOffsetFromUTC', timezone_offset_from_utc)
        self._set_attribute_if_not_none('ContributingEquipmentSequence', contributing_equipment_sequence)
        self._set_attribute_if_not_none('InstanceNumber', instance_number)
        
        if sop_instance_status is not None:
            self.SOPInstanceStatus = self._format_enum_value(sop_instance_status)
        if sop_authorization_datetime is not None:
            self.SOPAuthorizationDateTime = sop_authorization_datetime
        
        self._set_attribute_if_not_none('SOPAuthorizationComment', sop_authorization_comment)
        self._set_attribute_if_not_none('AuthorizationEquipmentCertificationNumber', authorization_equipment_certification_number)
        
        return self

    @staticmethod
    def create_coding_scheme_identification_item(
        coding_scheme_designator: str,
        coding_scheme_registry: str | None = None,
        coding_scheme_uid: str | None = None,
        coding_scheme_external_id: str | None = None,
        coding_scheme_name: str | None = None,
        coding_scheme_version: str | None = None,
        coding_scheme_responsible_organization: str | None = None
    ) -> Dataset:
        """Create coding scheme identification sequence item.

        Args:
            coding_scheme_designator (str): Coding scheme designator (0008,0102) Type 1
            coding_scheme_registry (str | None): External registry name (0008,0112) Type 1C
            coding_scheme_uid (str | None): Coding scheme UID (0008,010C) Type 1C
            coding_scheme_external_id (str | None): External ID (0008,0114) Type 2C
            coding_scheme_name (str | None): Full common name (0008,0115) Type 3
            coding_scheme_version (str | None): Version (0008,0103) Type 3
            coding_scheme_responsible_organization (str | None): Responsible organization (0008,0116) Type 3

        Returns:
            Dataset: Coding scheme identification sequence item
        """
        item = Dataset()
        item.CodingSchemeDesignator = coding_scheme_designator
        
        if coding_scheme_registry is not None:
            item.CodingSchemeRegistry = coding_scheme_registry
        if coding_scheme_uid is not None:
            item.CodingSchemeUID = coding_scheme_uid
        if coding_scheme_external_id is not None:
            item.CodingSchemeExternalID = coding_scheme_external_id
        if coding_scheme_name is not None:
            item.CodingSchemeName = coding_scheme_name
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if coding_scheme_responsible_organization is not None:
            item.CodingSchemeResponsibleOrganization = coding_scheme_responsible_organization
            
        return item

    @staticmethod
    def create_contributing_equipment_item(
        purpose_of_reference_code_sequence: list[dict[str, any]],
        manufacturer: str,
        institution_name: str | None = None,
        station_name: str | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        software_versions: list[str] | None = None,
        device_uid: str | None = None
    ) -> Dataset:
        """Create contributing equipment sequence item.

        Args:
            purpose_of_reference_code_sequence (list[dict]): Purpose of reference (0040,A170) Type 1
            manufacturer (str): Manufacturer of equipment (0008,0070) Type 1
            institution_name (str | None): Institution name (0008,0080) Type 3
            station_name (str | None): Station name (0008,1010) Type 3
            manufacturers_model_name (str | None): Model name (0008,1090) Type 3
            device_serial_number (str | None): Serial number (0018,1000) Type 3
            software_versions (list[str] | None): Software versions (0018,1020) Type 3
            device_uid (str | None): Device UID (0018,1002) Type 3

        Returns:
            Dataset: Contributing equipment sequence item
        """
        item = Dataset()
        item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        item.Manufacturer = manufacturer
        
        if institution_name is not None:
            item.InstitutionName = institution_name
        if station_name is not None:
            item.StationName = station_name
        if manufacturers_model_name is not None:
            item.ManufacturersModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if software_versions is not None:
            item.SoftwareVersions = software_versions
        if device_uid is not None:
            item.DeviceUID = device_uid
            
        return item

    @property
    def has_creation_info(self) -> bool:
        """Check if creation information is present.

        Returns:
            bool: True if creation date, time, or creator UID is present
        """
        return (hasattr(self, 'InstanceCreationDate') or
                hasattr(self, 'InstanceCreationTime') or
                hasattr(self, 'InstanceCreatorUID'))

    @property
    def is_synthetic(self) -> bool:
        """Check if this instance contains synthetic data.

        Returns:
            bool: True if synthetic data is YES
        """
        return getattr(self, 'SyntheticData', '') == 'YES'

    @property
    def has_authorization_info(self) -> bool:
        """Check if authorization information is present.

        Returns:
            bool: True if authorization status, datetime, or comment is present
        """
        return (hasattr(self, 'SOPInstanceStatus') or
                hasattr(self, 'SOPAuthorizationDateTime') or
                hasattr(self, 'SOPAuthorizationComment'))

    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate this SOP Common Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return SOPCommonValidator.validate(self, config)
