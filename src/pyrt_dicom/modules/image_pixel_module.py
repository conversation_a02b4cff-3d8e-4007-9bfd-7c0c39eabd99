"""
Image Pixel Module - DICOM PS3.3 C.7.6.3

The Image Pixel Module describes and encodes the pixel data of the image.
"""
from .base_module import BaseModule
from ..enums.image_enums import (
    PhotometricInterpretation, PlanarConfiguration, PixelRepresentation
)
from ..validators.modules.image_pixel_validator import ImagePixelValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class ImagePixelModule(BaseModule):
    """Image Pixel Module implementation for DICOM PS3.3 C.7.6.3.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes and encodes the pixel data of the image.
    
    Usage:
        # Create with required elements
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=12,
            high_bit=11,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        # Add conditional elements
        pixel.with_pixel_data(pixel_data=b'...')
        
        # Add palette color elements if needed
        pixel.with_palette_color(
            red_palette_descriptor=[256, 0, 16],
            green_palette_descriptor=[256, 0, 16],
            blue_palette_descriptor=[256, 0, 16],
            red_palette_data=b'...',
            green_palette_data=b'...',
            blue_palette_data=b'...'
        )
        
        # Add optional elements
        pixel.with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=4095
        )
        
        # Validate
        result = pixel.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        samples_per_pixel: int,
        photometric_interpretation: str | PhotometricInterpretation,
        rows: int,
        columns: int,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        pixel_representation: str | PixelRepresentation
    ) -> 'ImagePixelModule':
        """Create ImagePixelModule from all required (Type 1) data elements.
        
        Args:
            samples_per_pixel (int): Number of samples (planes) in image (0028,0002) Type 1
            photometric_interpretation (str | PhotometricInterpretation): Pixel data interpretation (0028,0004) Type 1
            rows (int): Number of rows in image (0028,0010) Type 1
            columns (int): Number of columns in image (0028,0011) Type 1
            bits_allocated (int): Bits allocated for each pixel sample (0028,0100) Type 1
            bits_stored (int): Bits stored for each pixel sample (0028,0101) Type 1
            high_bit (int): Most significant bit for pixel sample data (0028,0102) Type 1
            pixel_representation (str | PixelRepresentation): Data representation (0028,0103) Type 1
            
        Returns:
            ImagePixelModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.SamplesPerPixel = samples_per_pixel
        instance.PhotometricInterpretation = instance._format_enum_value(photometric_interpretation)
        instance.Rows = rows
        instance.Columns = columns
        instance.BitsAllocated = bits_allocated
        instance.BitsStored = bits_stored
        instance.HighBit = high_bit
        instance.PixelRepresentation = instance._format_enum_value(pixel_representation)
        return instance
    
    def with_pixel_data(
        self,
        pixel_data: bytes | None = None,
        pixel_data_provider_url: str | None = None
    ) -> 'ImagePixelModule':
        """Add pixel data (Type 1C - one of these is required).
        
        Args:
            pixel_data (bytes | None): Pixel data stream (7FE0,0010) Type 1C
            pixel_data_provider_url (str | None): URL of pixel data provider (0028,7FE0) Type 1C
            
        Returns:
            ImagePixelModule: Self with pixel data added
        """
        if pixel_data is not None and pixel_data_provider_url is not None:
            raise ValueError("Only one of pixel_data or pixel_data_provider_url should be provided")
        
        if pixel_data is not None:
            self.PixelData = pixel_data
        elif pixel_data_provider_url is not None:
            self.PixelDataProviderURL = pixel_data_provider_url
        
        return self
    
    def with_color_configuration(
        self,
        planar_configuration: int | PlanarConfiguration | None = None
    ) -> 'ImagePixelModule':
        """Add color configuration for multi-sample images.
        
        Note: Planar Configuration is Type 1C - required if Samples per Pixel > 1.
        
        Args:
            planar_configuration (int | PlanarConfiguration | None): Color encoding method (0028,0006) Type 1C
            
        Returns:
            ImagePixelModule: Self with color configuration added
        """
        if planar_configuration is not None:
            self.PlanarConfiguration = self._format_enum_value(planar_configuration)
        return self
    
    def with_pixel_aspect_ratio(
        self,
        pixel_aspect_ratio: list[int] | None = None
    ) -> 'ImagePixelModule':
        """Add pixel aspect ratio if not 1:1.
        
        Note: Pixel Aspect Ratio is Type 1C - required if aspect ratio is not 1:1 
        and physical pixel spacing is not specified.
        
        Args:
            pixel_aspect_ratio (list[int] | None): Vertical:horizontal pixel size ratio (0028,0034) Type 1C
            
        Returns:
            ImagePixelModule: Self with pixel aspect ratio added
        """
        self._set_attribute_if_not_none('PixelAspectRatio', pixel_aspect_ratio)
        return self
    
    def with_palette_color(
        self,
        red_palette_descriptor: list[int] | None = None,
        green_palette_descriptor: list[int] | None = None,
        blue_palette_descriptor: list[int] | None = None,
        red_palette_data: bytes | None = None,
        green_palette_data: bytes | None = None,
        blue_palette_data: bytes | None = None
    ) -> 'ImagePixelModule':
        """Add palette color lookup tables.
        
        Note: Palette descriptors and data are Type 1C - required if 
        Photometric Interpretation is PALETTE COLOR.
        
        Args:
            red_palette_descriptor (list[int] | None): Red palette format (0028,1101) Type 1C
            green_palette_descriptor (list[int] | None): Green palette format (0028,1102) Type 1C
            blue_palette_descriptor (list[int] | None): Blue palette format (0028,1103) Type 1C
            red_palette_data (bytes | None): Red palette data (0028,1201) Type 1C
            green_palette_data (bytes | None): Green palette data (0028,1202) Type 1C
            blue_palette_data (bytes | None): Blue palette data (0028,1203) Type 1C
            
        Returns:
            ImagePixelModule: Self with palette color elements added
        """
        self._set_attribute_if_not_none('RedPaletteColorLookupTableDescriptor', red_palette_descriptor)
        self._set_attribute_if_not_none('GreenPaletteColorLookupTableDescriptor', green_palette_descriptor)
        self._set_attribute_if_not_none('BluePaletteColorLookupTableDescriptor', blue_palette_descriptor)
        self._set_attribute_if_not_none('RedPaletteColorLookupTableData', red_palette_data)
        self._set_attribute_if_not_none('GreenPaletteColorLookupTableData', green_palette_data)
        self._set_attribute_if_not_none('BluePaletteColorLookupTableData', blue_palette_data)
        return self
    
    def with_optional_elements(
        self,
        smallest_image_pixel_value: int | None = None,
        largest_image_pixel_value: int | None = None,
        extended_offset_table: list[int] | None = None,
        extended_offset_table_lengths: list[int] | None = None,
        pixel_padding_range_limit: int | None = None,
        icc_profile: bytes | None = None,
        color_space: str | None = None
    ) -> 'ImagePixelModule':
        """Add optional (Type 3) data elements.
        
        Args:
            smallest_image_pixel_value (int | None): Minimum pixel value (0028,0106) Type 3
            largest_image_pixel_value (int | None): Maximum pixel value (0028,0107) Type 3
            extended_offset_table (list[int] | None): Frame byte offsets (7FE0,0001) Type 3
            extended_offset_table_lengths (list[int] | None): Frame byte lengths (7FE0,0002) Type 1C
            pixel_padding_range_limit (int | None): Pixel padding range limit (0028,0121) Type 1C
            icc_profile (bytes | None): ICC color profile (0028,2000) Type 3
            color_space (str | None): Color space identifier (0028,2002) Type 3
            
        Returns:
            ImagePixelModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('SmallestImagePixelValue', smallest_image_pixel_value)
        self._set_attribute_if_not_none('LargestImagePixelValue', largest_image_pixel_value)
        self._set_attribute_if_not_none('ExtendedOffsetTable', extended_offset_table)
        self._set_attribute_if_not_none('ExtendedOffsetTableLengths', extended_offset_table_lengths)
        self._set_attribute_if_not_none('PixelPaddingRangeLimit', pixel_padding_range_limit)
        self._set_attribute_if_not_none('ICCProfile', icc_profile)
        self._set_attribute_if_not_none('ColorSpace', color_space)
        return self
    
    @property
    def is_monochrome(self) -> bool:
        """Check if image is monochrome."""
        if not hasattr(self, 'PhotometricInterpretation'):
            return False
        return self.PhotometricInterpretation in ["MONOCHROME1", "MONOCHROME2"]
    
    @property
    def is_color(self) -> bool:
        """Check if image is color."""
        if not hasattr(self, 'PhotometricInterpretation'):
            return False
        return self.PhotometricInterpretation in ["RGB", "YBR_FULL", "YBR_FULL_422", "PALETTE COLOR"]
    
    @property
    def is_palette_color(self) -> bool:
        """Check if image uses palette color."""
        return hasattr(self, 'PhotometricInterpretation') and self.PhotometricInterpretation == "PALETTE COLOR"
    
    @property
    def is_signed(self) -> bool:
        """Check if pixel representation is signed."""
        return hasattr(self, 'PixelRepresentation') and self.PixelRepresentation == "0001H"
    
    @property
    def bytes_per_pixel(self) -> int | None:
        """Calculate bytes per pixel."""
        if not hasattr(self, 'BitsAllocated'):
            return None
        return (self.BitsAllocated + 7) // 8
    
    @property
    def total_pixels(self) -> int | None:
        """Calculate total number of pixels."""
        if not all(hasattr(self, attr) for attr in ['Rows', 'Columns']):
            return None
        return self.Rows * self.Columns
    
    @property
    def expected_pixel_data_size(self) -> int | None:
        """Calculate expected pixel data size in bytes."""
        total_pixels = self.total_pixels
        bytes_per_pixel = self.bytes_per_pixel
        if total_pixels is None or bytes_per_pixel is None:
            return None
        
        samples_per_pixel = getattr(self, 'SamplesPerPixel', 1)
        return total_pixels * samples_per_pixel * bytes_per_pixel
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate module data against DICOM standard."""
        return ImagePixelValidator.validate(self, config)
