"""
VOI LUT Module - DICOM PS3.3 C.11.2

The VOI LUT Module describes the VOI LUT transformation that converts
pixel values to values that are meaningful for display.
"""
from typing import Optional, Union, List, Dict, Any
from .base_module import BaseModule
from ..enums.image_enums import VoiLutFunction
from ..validators.modules.voi_lut_validator import VoiLutValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators.validation_result import ValidationResult


class VoiLutModule(BaseModule):
    """VOI LUT Module implementation for DICOM PS3.3 C.11.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes the VOI LUT transformation that converts pixel values to values
    that are meaningful for display.
    
    Usage:
        # Create with VOI LUT Sequence
        voi_lut = VoiLutModule.from_required_elements().with_voi_lut_sequence(
            voi_lut_sequence=[
                VoiLutModule.create_voi_lut_item(
                    lut_descriptor=[256, 0, 8],
                    lut_data=[0, 1, 2, 3],  # Example LUT data
                    lut_explanation="Standard display LUT"
                )
            ]
        )
        
        # Or create with Window Center/Width
        voi_lut = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[2048.0],
            window_width=[4096.0],
            window_explanation=["Standard CT window"],
            voi_lut_function=VoiLutFunction.LINEAR
        )
        
        # Validate
        result = voi_lut.validate()
    """
    
    @classmethod
    def from_required_elements(cls) -> 'VoiLutModule':
        """Create module with required elements.
        
        Note: This module has no Type 1 or Type 2 elements - all elements are conditional.
        Either VOI LUT Sequence or Window Center/Width must be provided via conditional methods.
        
        Returns:
            VoiLutModule: New module instance
        """
        return cls()
    
    def with_optional_elements(self, **kwargs) -> 'VoiLutModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The VOI LUT Module has no Type 3 elements defined in DICOM PS3.3 C.11.2.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            VoiLutModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"VoiLutModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    def with_voi_lut_sequence(
        self,
        voi_lut_sequence: List[Dict[str, Any]]
    ) -> 'VoiLutModule':
        """Add VOI LUT Sequence (Type 1C).
        
        Required if Window Center is not present. One or more Items shall be included.
        
        Args:
            voi_lut_sequence: List of VOI LUT items
            
        Returns:
            VoiLutModule: Self for method chaining
        """
        if not voi_lut_sequence:
            raise ValueError("VOI LUT Sequence must contain at least one item")
        
        self.VOILUTSequence = voi_lut_sequence
        return self
    
    def with_window_parameters(
        self,
        window_center: List[float],
        window_width: List[float],
        window_explanation: Optional[List[str]] = None,
        voi_lut_function: Optional[Union[VoiLutFunction, str]] = None
    ) -> 'VoiLutModule':
        """Add Window Center and Width parameters (Type 1C).
        
        Required if VOI LUT Sequence is not present. Window Center and Width
        must have the same number of values and are considered as pairs.
        
        Args:
            window_center: Window Center for display
            window_width: Window Width for display (must be >= 1)
            window_explanation: Optional explanation of window meaning
            voi_lut_function: Optional VOI LUT function to apply
            
        Returns:
            VoiLutModule: Self for method chaining
        """
        if len(window_center) != len(window_width):
            raise ValueError("Window Center and Width must have the same number of values")
        
        if any(w < 1 for w in window_width):
            raise ValueError("Window Width must be >= 1")
        
        # Convert to DICOM string format
        self.WindowCenter = "\\".join(str(c) for c in window_center)
        self.WindowWidth = "\\".join(str(w) for w in window_width)
        
        if window_explanation is not None:
            if len(window_explanation) != len(window_center):
                raise ValueError("Window explanation must match number of window center/width pairs")
            self.WindowCenterWidthExplanation = "\\".join(window_explanation)
        
        if voi_lut_function is not None:
            self.VOILUTFunction = self._format_enum_value(voi_lut_function)
        
        return self
    
    @staticmethod
    def create_voi_lut_item(
        lut_descriptor: List[int],
        lut_data: List[int],
        lut_explanation: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a VOI LUT Sequence item.
        
        Args:
            lut_descriptor: Format of the LUT Data [entries, first_value, bits_per_entry]
            lut_data: LUT Data values
            lut_explanation: Optional free form text explanation of the LUT meaning
            
        Returns:
            Dict representing a VOI LUT Sequence item
        """
        item = {
            'LUTDescriptor': lut_descriptor,
            'LUTData': lut_data
        }
        
        if lut_explanation is not None:
            item['LUTExplanation'] = lut_explanation
            
        return item
    
    @property
    def has_voi_lut_sequence(self) -> bool:
        """Check if VOI LUT Sequence is present.
        
        Returns:
            bool: True if VOI LUT Sequence is present
        """
        return hasattr(self, 'VOILUTSequence')
    
    @property
    def has_window_parameters(self) -> bool:
        """Check if Window parameters are present.
        
        Returns:
            bool: True if both Window Center and Width are present
        """
        return (hasattr(self, 'WindowCenter') and hasattr(self, 'WindowWidth'))
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if either LUT sequence or window parameters are present
        """
        return self.has_voi_lut_sequence or self.has_window_parameters
    
    @property
    def has_optional_elements(self) -> bool:
        """Check if optional elements are present.
        
        Returns:
            bool: True if window explanation or VOI LUT function is present
        """
        return (hasattr(self, 'WindowCenterWidthExplanation') or 
                hasattr(self, 'VOILUTFunction'))
    
    def validate(self, config: Optional[ValidationConfig] = None) -> ValidationResult:
        """Validate this VOI LUT Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return VoiLutValidator.validate(self, config)
