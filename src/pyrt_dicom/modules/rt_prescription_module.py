"""
RT Prescription Module - DICOM PS3.3 C.8.8.10

The RT Prescription Module contains prescription information for radiotherapy treatment.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation
from ..validators.modules.rt_prescription_validator import RTPrescriptionValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class RTPrescriptionModule(BaseModule):
    """RT Prescription Module implementation for DICOM PS3.3 C.8.8.10.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains prescription information for radiotherapy treatment.
    
    Usage:
        # Create with optional elements (no required elements at top level)
        prescription = RTPrescriptionModule.from_required_elements()
        
        # Add optional elements
        prescription.with_optional_elements(
            prescription_description="Primary treatment prescription",
            dose_reference_sequence=[
                prescription.create_dose_reference_item(
                    dose_reference_number=1,
                    dose_reference_structure_type=DoseReferenceStructureType.POINT,
                    dose_reference_type=DoseReferenceType.TARGET,
                    referenced_roi_number=1,
                    target_prescription_dose=200.0
                )
            ]
        )
        
        # Validate
        result = prescription.validate()
    """

    @classmethod
    def from_required_elements(
        cls
    ) -> 'RTPrescriptionModule':
        """Create RTPrescriptionModule from all required data elements.
        
        Note: This module has no required (Type 1 or Type 2) elements at the top level.
        All elements are Type 3 (optional).
                
        Returns:
            RTPrescriptionModule: New module instance
        """
        instance = cls()
        return instance
    
    def with_optional_elements(
        self,
        prescription_description: str | None = None,
        dose_reference_sequence: list[dict[str, any]] | None = None
    ) -> 'RTPrescriptionModule':
        """Add optional (Type 3) elements.
        
        Args:
            prescription_description (str | None): User-defined description of treatment prescription (300A,000E) Type 3
            dose_reference_sequence (list[dict[str, any]] | None): Sequence of Dose References (300A,0010) Type 3
            
        Returns:
            RTPrescriptionModule: Self for method chaining
        """
        self._set_attribute_if_not_none('PrescriptionDescription', prescription_description)
        self._set_attribute_if_not_none('DoseReferenceSequence', dose_reference_sequence)
        return self
    
    @staticmethod
    def create_dose_reference_item(
        dose_reference_number: int,
        dose_reference_structure_type: str | DoseReferenceStructureType,
        dose_reference_type: str | DoseReferenceType,
        dose_reference_uid: str | None = None,
        dose_value_purpose: str | DoseValuePurpose | None = None,
        dose_value_interpretation: str | DoseValueInterpretation | None = None,
        dose_reference_description: str | None = None,
        referenced_roi_number: int | None = None,
        dose_reference_point_coordinates: list[float] | None = None,
        nominal_prior_dose: float | None = None,
        constraint_weight: float | None = None,
        delivery_warning_dose: float | None = None,
        delivery_maximum_dose: float | None = None,
        target_minimum_dose: float | None = None,
        target_prescription_dose: float | None = None,
        target_maximum_dose: float | None = None,
        target_underdose_volume_fraction: float | None = None,
        organ_at_risk_full_volume_dose: float | None = None,
        organ_at_risk_limit_dose: float | None = None,
        organ_at_risk_maximum_dose: float | None = None,
        organ_at_risk_overdose_volume_fraction: float | None = None
    ) -> Dataset:
        """Create dose reference sequence item.
        
        Args:
            dose_reference_number (int): Identification number of the Dose Reference (300A,0012) Type 1
            dose_reference_structure_type (str | DoseReferenceStructureType): Structure type of Dose Reference (300A,0014) Type 1
            dose_reference_type (str | DoseReferenceType): Type of Dose Reference (300A,0020) Type 1
            dose_reference_uid (str | None): Unique identifier for Dose Reference (300A,0013) Type 3
            dose_value_purpose (str | DoseValuePurpose | None): Purpose for dose values (300A,061D) Type 3
            dose_value_interpretation (str | DoseValueInterpretation | None): Interpretation of dose values (300A,068B) Type 3
            dose_reference_description (str | None): User-defined description (300A,0016) Type 3
            referenced_roi_number (int | None): ROI Number for dose reference (3006,0084) Type 1C
            dose_reference_point_coordinates (list[float] | None): Coordinates of Reference Point (300A,0018) Type 1C
            nominal_prior_dose (float | None): Dose from prior treatment (300A,001A) Type 3
            constraint_weight (float | None): Relative importance of constraint (300A,0021) Type 3
            delivery_warning_dose (float | None): Warning dose threshold (300A,0022) Type 3
            delivery_maximum_dose (float | None): Maximum deliverable dose (300A,0023) Type 3
            target_minimum_dose (float | None): Minimum permitted dose to target (300A,0025) Type 3
            target_prescription_dose (float | None): Prescribed dose to target (300A,0026) Type 3
            target_maximum_dose (float | None): Maximum permitted dose to target (300A,0027) Type 3
            target_underdose_volume_fraction (float | None): Maximum underdose volume fraction (300A,0028) Type 3
            organ_at_risk_full_volume_dose (float | None): Maximum dose to entire OAR (300A,002A) Type 3
            organ_at_risk_limit_dose (float | None): Maximum permitted dose to any part of OAR (300A,002B) Type 3
            organ_at_risk_maximum_dose (float | None): Maximum dose to non-overdosed part of OAR (300A,002C) Type 3
            organ_at_risk_overdose_volume_fraction (float | None): Maximum overdose volume fraction (300A,002D) Type 3
            
        Returns:
            Dataset: Dose reference sequence item
        """
        # Validate conditional requirements
        structure_type_str = dose_reference_structure_type.value if hasattr(dose_reference_structure_type, 'value') else str(dose_reference_structure_type)

        if structure_type_str in ['POINT', 'VOLUME'] and referenced_roi_number is None:
            raise ValueError("Referenced ROI Number is required when Dose Reference Structure Type is POINT or VOLUME")

        if structure_type_str == 'COORDINATES' and dose_reference_point_coordinates is None:
            raise ValueError("Dose Reference Point Coordinates is required when Dose Reference Structure Type is COORDINATES")

        item = Dataset()
        item.DoseReferenceNumber = dose_reference_number
        item.DoseReferenceStructureType = structure_type_str
        item.DoseReferenceType = dose_reference_type.value if hasattr(dose_reference_type, 'value') else str(dose_reference_type)
        
        # Add optional elements if provided
        if dose_reference_uid is not None:
            item.DoseReferenceUID = dose_reference_uid
        if dose_value_purpose is not None:
            item.DoseValuePurpose = dose_value_purpose.value if hasattr(dose_value_purpose, 'value') else str(dose_value_purpose)
        if dose_value_interpretation is not None:
            item.DoseValueInterpretation = dose_value_interpretation.value if hasattr(dose_value_interpretation, 'value') else str(dose_value_interpretation)
        if dose_reference_description is not None:
            item.DoseReferenceDescription = dose_reference_description
        if referenced_roi_number is not None:
            item.ReferencedROINumber = referenced_roi_number
        if dose_reference_point_coordinates is not None:
            item.DoseReferencePointCoordinates = dose_reference_point_coordinates
        if nominal_prior_dose is not None:
            item.NominalPriorDose = nominal_prior_dose
        if constraint_weight is not None:
            item.ConstraintWeight = constraint_weight
        if delivery_warning_dose is not None:
            item.DeliveryWarningDose = delivery_warning_dose
        if delivery_maximum_dose is not None:
            item.DeliveryMaximumDose = delivery_maximum_dose
        if target_minimum_dose is not None:
            item.TargetMinimumDose = target_minimum_dose
        if target_prescription_dose is not None:
            item.TargetPrescriptionDose = target_prescription_dose
        if target_maximum_dose is not None:
            item.TargetMaximumDose = target_maximum_dose
        if target_underdose_volume_fraction is not None:
            item.TargetUnderdoseVolumeFraction = target_underdose_volume_fraction
        if organ_at_risk_full_volume_dose is not None:
            item.OrganAtRiskFullvolumeDose = organ_at_risk_full_volume_dose
        if organ_at_risk_limit_dose is not None:
            item.OrganAtRiskLimitDose = organ_at_risk_limit_dose
        if organ_at_risk_maximum_dose is not None:
            item.OrganAtRiskMaximumDose = organ_at_risk_maximum_dose
        if organ_at_risk_overdose_volume_fraction is not None:
            item.OrganAtRiskOverdoseVolumeFraction = organ_at_risk_overdose_volume_fraction

        return item
    
    @property
    def has_prescription(self) -> bool:
        """Check if prescription information is present.
        
        Returns:
            bool: True if prescription description or dose references are present
        """
        return (hasattr(self, 'PrescriptionDescription') or 
                hasattr(self, 'DoseReferenceSequence'))
    
    @property
    def dose_reference_count(self) -> int:
        """Get the number of dose references in this module.
        
        Returns:
            int: Number of dose references in Dose Reference Sequence
        """
        dose_ref_sequence = getattr(self, 'DoseReferenceSequence', [])
        return len(dose_ref_sequence)
    
    def get_dose_reference_types(self) -> list[str]:
        """Get list of dose reference types present in this module.
        
        Returns:
            list[str]: List of unique dose reference types
        """
        dose_ref_sequence = getattr(self, 'DoseReferenceSequence', [])
        reference_types = []
        for dose_ref_item in dose_ref_sequence:
            ref_type = dose_ref_item.get('DoseReferenceType', '')
            if ref_type and ref_type not in reference_types:
                reference_types.append(ref_type)
        return reference_types
    
    def get_target_dose_references(self) -> list[dict[str, any]]:
        """Get dose references that are targets.
        
        Returns:
            list[dict[str, any]]: List of target dose reference items
        """
        dose_ref_sequence = getattr(self, 'DoseReferenceSequence', [])
        target_refs = []
        for dose_ref_item in dose_ref_sequence:
            if dose_ref_item.get('DoseReferenceType', '') == 'TARGET':
                target_refs.append(dose_ref_item)
        return target_refs
    
    def get_organ_at_risk_dose_references(self) -> list[dict[str, any]]:
        """Get dose references that are organs at risk.
        
        Returns:
            list[dict[str, any]]: List of organ at risk dose reference items
        """
        dose_ref_sequence = getattr(self, 'DoseReferenceSequence', [])
        oar_refs = []
        for dose_ref_item in dose_ref_sequence:
            if dose_ref_item.get('DoseReferenceType', '') == 'ORGAN_AT_RISK':
                oar_refs.append(dose_ref_item)
        return oar_refs
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate this RT Prescription Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        return RTPrescriptionValidator.validate(self, config)
