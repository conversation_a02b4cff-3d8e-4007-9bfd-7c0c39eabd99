"""
General Reference Module - DICOM PS3.3 C.12.4

The General Reference Module references source and other related Instances 
and describes the manner of derivation.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.common_enums import SpatialLocationsPreserved
from ..validators.modules.general_reference_validator import GeneralReferenceValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class GeneralReferenceModule(BaseModule):
    """General Reference Module implementation for DICOM PS3.3 C.12.4.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    References source and other related Instances and describes the manner of derivation.
    
    Usage:
        # Create with no required elements (all are Type 3)
        reference = GeneralReferenceModule.from_required_elements()
        
        # Add optional elements
        reference.with_optional_elements(
            derivation_description="Contrast enhanced image",
            referenced_image_sequence=[
                reference.create_referenced_image_item(
                    referenced_sop_class_uid="1.2.840.10008.*******.1.2",
                    referenced_sop_instance_uid="*******.*******.9"
                )
            ]
        )
        
        # Validate
        result = reference.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'GeneralReferenceModule':
        """Create GeneralReferenceModule with no required elements (all are Type 3).
        
        Returns:
            GeneralReferenceModule: New module instance
        """
        return cls()
    
    def with_optional_elements(
        self,
        referenced_image_sequence: list[dict[str, any]] | None = None,
        referenced_instance_sequence: list[dict[str, any]] | None = None,
        derivation_description: str | None = None,
        derivation_code_sequence: list[dict[str, any]] | None = None,
        source_image_sequence: list[dict[str, any]] | None = None,
        source_instance_sequence: list[dict[str, any]] | None = None
    ) -> 'GeneralReferenceModule':
        """Add optional (Type 3) elements.
        
        Args:
            referenced_image_sequence (list[dict] | None): Other images significantly related (0008,1140) Type 3
            referenced_instance_sequence (list[dict] | None): Non-image composite SOP Instances (0008,114A) Type 3
            derivation_description (str | None): Text description of derivation (0008,2111) Type 3
            derivation_code_sequence (list[dict] | None): Coded description of derivation (0008,9215) Type 3
            source_image_sequence (list[dict] | None): Source images used to derive this image (0008,2112) Type 3
            source_instance_sequence (list[dict] | None): Source non-image instances (0042,0013) Type 3
        """
        self._set_attribute_if_not_none('ReferencedImageSequence', referenced_image_sequence)
        self._set_attribute_if_not_none('ReferencedInstanceSequence', referenced_instance_sequence)
        self._set_attribute_if_not_none('DerivationDescription', derivation_description)
        self._set_attribute_if_not_none('DerivationCodeSequence', derivation_code_sequence)
        self._set_attribute_if_not_none('SourceImageSequence', source_image_sequence)
        self._set_attribute_if_not_none('SourceInstanceSequence', source_instance_sequence)
        return self
    
    @staticmethod
    def create_referenced_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None,
        purpose_of_reference_code_sequence: list[dict[str, any]] | None = None
    ) -> Dataset:
        """Create referenced image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_frame_number (list[int] | None): Referenced frame numbers
            purpose_of_reference_code_sequence (list[dict] | None): Purpose of reference code

        Returns:
            Dataset: Referenced image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if purpose_of_reference_code_sequence is not None:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        return item
    
    @staticmethod
    def create_source_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None,
        purpose_of_reference_code_sequence: list[dict[str, any]] | None = None,
        spatial_locations_preserved: str | SpatialLocationsPreserved | None = None,
        patient_orientation: list[str] | None = None
    ) -> Dataset:
        """Create source image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_frame_number (list[int] | None): Referenced frame numbers
            purpose_of_reference_code_sequence (list[dict] | None): Purpose of reference code
            spatial_locations_preserved (str | SpatialLocationsPreserved | None): Spatial locations preserved
            patient_orientation (list[str] | None): Patient orientation (Type 1C if spatial_locations_preserved is REORIENTED_ONLY)

        Returns:
            Dataset: Source image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if purpose_of_reference_code_sequence is not None:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        if spatial_locations_preserved is not None:
            item.SpatialLocationsPreserved = spatial_locations_preserved.value if hasattr(spatial_locations_preserved, 'value') else str(spatial_locations_preserved)
        if patient_orientation is not None:
            item.PatientOrientation = patient_orientation
        return item
    
    @staticmethod
    def create_referenced_instance_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        purpose_of_reference_code_sequence: list[dict[str, any]]
    ) -> Dataset:
        """Create referenced instance sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            purpose_of_reference_code_sequence (list[dict]): Purpose of reference code (Type 1)

        Returns:
            Dataset: Referenced instance sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        return item
    
    @property
    def has_referenced_images(self) -> bool:
        """Check if referenced images are present.
        
        Returns:
            bool: True if referenced image sequence is present
        """
        return hasattr(self, 'ReferencedImageSequence')
    
    @property
    def has_derivation_info(self) -> bool:
        """Check if derivation information is present.
        
        Returns:
            bool: True if derivation description or code sequence is present
        """
        return (hasattr(self, 'DerivationDescription') or 
                hasattr(self, 'DerivationCodeSequence'))
    
    @property
    def has_source_images(self) -> bool:
        """Check if source images are present.
        
        Returns:
            bool: True if source image sequence is present
        """
        return hasattr(self, 'SourceImageSequence')
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate this General Reference Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult object
        """
        return GeneralReferenceValidator.validate(self, config)
