"""
Synchronization Module - DICOM PS3.3 C.7.4.2

The Synchronization Module contains attributes necessary to uniquely identify 
a Frame of Reference that establishes the temporal relationship of SOP Instances.
A synchronized environment may be established based on a shared time of day clock,
and/or on a shared trigger event or synchronization waveform channel.
"""
from .base_module import BaseModule
from ..validators.modules.synchronization_validator import SynchronizationValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators.validation_result import ValidationResult
from ..enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol
)


class SynchronizationModule(BaseModule):
    """Synchronization Module implementation for DICOM PS3.3 C.7.4.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes necessary to uniquely identify a Frame of Reference that 
    establishes the temporal relationship of SOP Instances.
    
    Usage:
        # Create with required elements
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )

        # Add optional elements
        sync.with_optional_elements(
            trigger_source_or_type="LINAC_001",
            time_source="NTP_SERVER_001",
            time_distribution_protocol=TimeDistributionProtocol.NTP,
            ntp_source_address="*************"
        )
        
        # Add conditional elements if needed
        sync.with_synchronization_channel(
            synchronization_channel=[1, 2]  # Multiplex group 1, channel 2
        )
        
        # Validate
        result = sync.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        synchronization_frame_of_reference_uid: str,
        synchronization_trigger: str | SynchronizationTrigger,
        acquisition_time_synchronized: str | AcquisitionTimeSynchronized
    ) -> 'SynchronizationModule':
        """Create Synchronization Module from all required (Type 1) data elements.
        
        Args:
            synchronization_frame_of_reference_uid (str): UID of common synchronization 
                environment (0020,0200) Type 1. Identifies shared synchronization environment.
            synchronization_trigger (str | SynchronizationTrigger): Data acquisition synchronization
                with external equipment (0018,106A) Type 1. Values: SOURCE, EXTERNAL, PASSTHRU, NO TRIGGER.
            acquisition_time_synchronized (str | AcquisitionTimeSynchronized): Acquisition DateTime
                synchronized with external time reference (0018,1800) Type 1. Values: Y, N.
            
        Returns:
            SynchronizationModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.SynchronizationFrameOfReferenceUID = synchronization_frame_of_reference_uid
        instance.SynchronizationTrigger = instance._format_enum_value(synchronization_trigger)
        instance.AcquisitionTimeSynchronized = instance._format_enum_value(acquisition_time_synchronized)
        return instance
    
    def with_optional_elements(
        self,
        trigger_source_or_type: str | None = None,
        time_source: str | None = None,
        time_distribution_protocol: str | TimeDistributionProtocol | None = None,
        ntp_source_address: str | None = None
    ) -> 'SynchronizationModule':
        """Add optional (Type 3) data elements.
        
        Args:
            trigger_source_or_type (str | None): Equipment ID of trigger source and/or 
                type of trigger (0018,1061) Type 3.
            time_source (str | None): ID of equipment or system providing time reference 
                (0018,1801) Type 3.
            time_distribution_protocol (str | TimeDistributionProtocol | None): Method of time
                distribution used to synchronize equipment (0018,1802) Type 3. Values: NTP, IRIG, GPS, SNTP, PTP.
            ntp_source_address (str | None): IP Address of NTP, SNTP, or PTP time source 
                (0018,1803) Type 3. IPv4 in dotted decimal or IPv6 in colon separated hex.
            
        Returns:
            SynchronizationModule: Self for method chaining
        """
        self._set_attribute_if_not_none('TriggerSourceOrType', trigger_source_or_type)
        self._set_attribute_if_not_none('TimeSource', time_source)
        self._set_attribute_if_not_none('TimeDistributionProtocol', self._format_enum_value(time_distribution_protocol) if time_distribution_protocol is not None else None)
        self._set_attribute_if_not_none('NTPSourceAddress', ntp_source_address)
        return self
    
    def with_synchronization_channel(
        self,
        synchronization_channel: list[int]
    ) -> 'SynchronizationModule':
        """Add synchronization channel (Type 1C) - required if synchronization channel 
        or trigger is encoded in a waveform in this SOP Instance.
        
        Args:
            synchronization_channel (list[int]): Identifier of waveform channel that records 
                the synchronization channel or trigger (0018,106C) Type 1C. Specified as 
                pair [M,C] where M is multiplex group ordinal and C is channel number ordinal.
            
        Returns:
            SynchronizationModule: Self for method chaining
        """
        # Validate that we have exactly 2 values for [M,C] pair
        if len(synchronization_channel) != 2:
            raise ValueError("Synchronization Channel must be specified as [M,C] pair with exactly 2 values")
        
        self.SynchronizationChannel = synchronization_channel
        return self
    
    @property
    def is_source_synchronized(self) -> bool:
        """Check if this equipment provides synchronization to other equipment.
        
        Returns:
            bool: True if synchronization trigger is SOURCE
        """
        return getattr(self, 'SynchronizationTrigger', '') == 'SOURCE'
    
    @property
    def is_externally_synchronized(self) -> bool:
        """Check if this equipment receives synchronization from external equipment.
        
        Returns:
            bool: True if synchronization trigger is EXTERNAL
        """
        return getattr(self, 'SynchronizationTrigger', '') == 'EXTERNAL'
    
    @property
    def is_passthrough_synchronized(self) -> bool:
        """Check if this equipment receives and forwards synchronization.
        
        Returns:
            bool: True if synchronization trigger is PASSTHRU
        """
        return getattr(self, 'SynchronizationTrigger', '') == 'PASSTHRU'
    
    @property
    def has_no_trigger(self) -> bool:
        """Check if data acquisition is not synchronized by common channel or trigger.
        
        Returns:
            bool: True if synchronization trigger is NO TRIGGER
        """
        return getattr(self, 'SynchronizationTrigger', '') == 'NO TRIGGER'
    
    @property
    def is_time_synchronized(self) -> bool:
        """Check if acquisition time is synchronized with external time reference.
        
        Returns:
            bool: True if acquisition time synchronized is Y
        """
        return getattr(self, 'AcquisitionTimeSynchronized', '') == 'Y'
    
    @property
    def has_synchronization_channel(self) -> bool:
        """Check if synchronization channel is specified.
        
        Returns:
            bool: True if synchronization channel is present
        """
        return hasattr(self, 'SynchronizationChannel')
    
    @property
    def uses_ntp_protocol(self) -> bool:
        """Check if time distribution uses Network Time Protocol.
        
        Returns:
            bool: True if time distribution protocol is NTP or SNTP
        """
        protocol = getattr(self, 'TimeDistributionProtocol', '')
        return protocol in ['NTP', 'SNTP']
    
    @property
    def uses_precision_time_protocol(self) -> bool:
        """Check if time distribution uses IEEE 1588 Precision Time Protocol.
        
        Returns:
            bool: True if time distribution protocol is PTP
        """
        return getattr(self, 'TimeDistributionProtocol', '') == 'PTP'
    
    def validate(self, config: ValidationConfig = None) -> ValidationResult:
        """Validate Synchronization Module data against DICOM standard.
        
        Args:
            config (ValidationConfig | None): Validation configuration options
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return SynchronizationValidator.validate(self, config)
